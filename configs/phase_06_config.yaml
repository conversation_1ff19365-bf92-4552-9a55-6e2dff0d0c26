# Phase 6: Note Candidate Window Detection Configuration
# Configuration for detecting note candidate windows from aligned beats and onsets

# Input/Output Paths
paths:
  input_dirs:
    phase3: "data/processed/phase3"
    phase4: "data/processed/phase4" 
    phase5: "data/processed/phase5"
  output_dir: "data/processed/phase6"

# Candidate Detection Parameters
detection:
  # Window size for candidate detection (seconds)
  window_size: 0.15
  
  # Beat subdivision levels to consider
  subdivision_levels:
    - "quarter"
    - "eighth" 
    - "sixteenth"
    - "triplet"
  
  # Confidence thresholds
  confidence_threshold: 0.3
  subdivision_confidence_multiplier: 0.7
  onset_confidence_multiplier: 1.5
  
  # Overlap handling
  overlap_threshold: 0.5
  
  # Candidate limits per song (optimized thresholds)
  max_candidates_per_song: 2000
  min_candidates_per_song: 25  # Reduced from 50 to accommodate shorter songs
  
  # Audio processing
  sample_rate: 22050

# Feature Extraction Parameters
feature_extraction:
  # MFCC parameters
  n_mfcc: 13
  n_fft: 2048
  hop_length: 512
  
  # Spectral feature parameters
  spectral_features:
    - "spectral_centroid"
    - "spectral_rolloff" 
    - "spectral_bandwidth"
    - "zero_crossing_rate"

# Confidence Calculation Weights
confidence_weights:
  onset_weight: 0.4
  energy_weight: 0.3
  spectral_weight: 0.3
  
  # Subdivision penalties
  subdivision_penalties:
    quarter: 1.0
    eighth: 0.8
    sixteenth: 0.6
    triplet: 0.7
    syncopated: 0.5

# Processing Parameters
processing:
  # Batch processing
  batch_size: 10
  
  # Memory management
  gc_frequency: 10  # Garbage collection every N songs
  
  # Performance limits
  max_processing_time_seconds: 120
  max_memory_usage_mb: 3072
  max_audio_snippet_size_mb: 1

# Quality Control (fine-tuned thresholds for edge case handling)
quality_gates:
  min_detection_coverage: 0.8
  min_strong_candidate_ratio: 0.25  # Increased from 0.2 for better quality assurance
  max_false_positive_estimate: 0.4

# Logging Configuration
logging:
  level: "INFO"
  log_to_file: true
  log_to_console: true
  detailed_timing: false

# Validation Parameters
validation:
  # Enable validation mode (process subset)
  validation_mode: false
  validation_subset_size: 10
  
  # Quality metrics
  enable_quality_metrics: true
  save_visualizations: false
  
  # Performance monitoring
  monitor_memory_usage: true
  monitor_processing_time: true

# Output Format Configuration
output:
  # JSON formatting
  json_indent: 2
  save_audio_snippets: true
  compress_features: false
  
  # File naming
  candidate_file_suffix: "_candidates"
  features_file_suffix: "_features" 
  stats_file_suffix: "_stats"
  
  # Directory structure
  subdirectories:
    - "note_candidates"
    - "candidate_audio"
    - "candidate_features" 
    - "detection_stats"
    - "logs"
