#!/usr/bin/env python3
"""
Analyze patterns in songs with low strong candidate ratios.

This script performs detailed analysis of songs with strong candidate ratios
below 30% to identify patterns and recommend parameter adjustments.

Author: TJAGen Algorithm Analysis
Version: 1.0.0
"""

import json
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from typing import Dict, List, <PERSON><PERSON>

def load_song_data(song_name: str) -> <PERSON><PERSON>[List[Dict], Dict, Dict]:
    """Load all data for a specific song."""
    
    base_dir = Path("data/processed/phase6")
    
    # Load candidates
    candidates_file = base_dir / "note_candidates" / f"{song_name}_candidates.json"
    with open(candidates_file, 'r') as f:
        candidates = json.load(f)
    
    # Load features
    features_file = base_dir / "candidate_features" / f"{song_name}_features.json"
    with open(features_file, 'r') as f:
        features = json.load(f)
    
    # Load stats
    stats_file = base_dir / "detection_stats" / f"{song_name}_stats.json"
    with open(stats_file, 'r') as f:
        stats = json.load(f)
    
    return candidates, features, stats

def analyze_confidence_components(candidates: List[Dict]) -> Dict:
    """Analyze the individual components of confidence calculation."""
    
    analysis = {
        "onset_strengths": [],
        "energy_maxes": [],
        "spectral_bandwidths": [],
        "subdivisions": [],
        "confidences": [],
        "candidate_types": []
    }
    
    for candidate in candidates:
        analysis["onset_strengths"].append(candidate["onset_strength"])
        analysis["spectral_bandwidths"].append(candidate["spectral_features"]["spectral_bandwidth"])
        analysis["subdivisions"].append(candidate["beat_subdivision"])
        analysis["confidences"].append(candidate["candidate_confidence"])
        analysis["candidate_types"].append(candidate["candidate_type"])
        
        # Calculate energy max from profile
        energy_profile = candidate["energy_profile"]
        energy_max = max(energy_profile) if energy_profile else 0
        analysis["energy_maxes"].append(energy_max)
    
    # Convert to numpy arrays for analysis
    for key in ["onset_strengths", "energy_maxes", "spectral_bandwidths", "confidences"]:
        analysis[key] = np.array(analysis[key])
    
    return analysis

def calculate_component_contributions(analysis: Dict) -> Dict:
    """Calculate how each component contributes to confidence."""
    
    # Current algorithm parameters
    onset_scaling = 0.5
    energy_scaling = 5.0
    spectral_divisor = 1500.0
    
    # Calculate component confidences
    onset_confidences = np.minimum(1.0, analysis["onset_strengths"] * onset_scaling)
    energy_confidences = np.minimum(1.0, analysis["energy_maxes"] * energy_scaling)
    spectral_confidences = np.minimum(1.0, analysis["spectral_bandwidths"] / spectral_divisor)
    
    # Calculate subdivision weights
    subdivision_weights = []
    weight_map = {"quarter": 1.0, "eighth": 0.8, "sixteenth": 0.6, "triplet": 0.7, "syncopated": 0.5}
    for subdivision in analysis["subdivisions"]:
        subdivision_weights.append(weight_map.get(subdivision, 0.5))
    subdivision_weights = np.array(subdivision_weights)
    
    # Calculate weighted contributions
    onset_contributions = onset_confidences * 0.4 * subdivision_weights
    energy_contributions = energy_confidences * 0.3 * subdivision_weights
    spectral_contributions = spectral_confidences * 0.3 * subdivision_weights
    
    return {
        "onset_confidences": onset_confidences,
        "energy_confidences": energy_confidences,
        "spectral_confidences": spectral_confidences,
        "subdivision_weights": subdivision_weights,
        "onset_contributions": onset_contributions,
        "energy_contributions": energy_contributions,
        "spectral_contributions": spectral_contributions
    }

def identify_bottlenecks(analysis: Dict, contributions: Dict) -> Dict:
    """Identify which components are limiting strong candidate detection."""
    
    bottlenecks = {
        "onset_limited": 0,
        "energy_limited": 0,
        "spectral_limited": 0,
        "subdivision_limited": 0,
        "total_candidates": len(analysis["confidences"])
    }
    
    # Find candidates that could be strong but are limited by specific components
    for i in range(len(analysis["confidences"])):
        confidence = analysis["confidences"][i]
        
        if confidence < 0.6:  # Not strong
            # Check which component is the limiting factor
            onset_contrib = contributions["onset_contributions"][i]
            energy_contrib = contributions["energy_contributions"][i]
            spectral_contrib = contributions["spectral_contributions"][i]
            
            # Find the weakest component
            components = [
                ("onset", onset_contrib),
                ("energy", energy_contrib),
                ("spectral", spectral_contrib)
            ]
            
            weakest = min(components, key=lambda x: x[1])
            bottlenecks[f"{weakest[0]}_limited"] += 1
            
            # Check subdivision penalty
            if contributions["subdivision_weights"][i] < 1.0:
                bottlenecks["subdivision_limited"] += 1
    
    return bottlenecks

def recommend_parameter_adjustments(song_name: str, analysis: Dict, contributions: Dict, bottlenecks: Dict) -> Dict:
    """Recommend parameter adjustments based on analysis."""
    
    recommendations = {
        "song": song_name,
        "current_strong_ratio": np.sum(analysis["confidences"] > 0.6) / len(analysis["confidences"]),
        "adjustments": []
    }
    
    total_weak = bottlenecks["total_candidates"] - np.sum(analysis["confidences"] > 0.6)
    
    if bottlenecks["onset_limited"] > total_weak * 0.3:
        recommendations["adjustments"].append({
            "parameter": "onset_scaling",
            "current": 0.5,
            "recommended": 0.6,
            "reason": f"Onset strength limiting {bottlenecks['onset_limited']} candidates"
        })
    
    if bottlenecks["energy_limited"] > total_weak * 0.3:
        recommendations["adjustments"].append({
            "parameter": "energy_scaling",
            "current": 5.0,
            "recommended": 6.0,
            "reason": f"Energy scaling limiting {bottlenecks['energy_limited']} candidates"
        })
    
    if bottlenecks["spectral_limited"] > total_weak * 0.3:
        recommendations["adjustments"].append({
            "parameter": "spectral_divisor",
            "current": 1500.0,
            "recommended": 1200.0,
            "reason": f"Spectral bandwidth limiting {bottlenecks['spectral_limited']} candidates"
        })
    
    if bottlenecks["subdivision_limited"] > total_weak * 0.4:
        recommendations["adjustments"].append({
            "parameter": "subdivision_penalties",
            "current": "eighth: 0.8, sixteenth: 0.6",
            "recommended": "eighth: 0.9, sixteenth: 0.7",
            "reason": f"Subdivision penalties limiting {bottlenecks['subdivision_limited']} candidates"
        })
    
    return recommendations

def analyze_genre_patterns(low_ratio_songs: List[str]) -> Dict:
    """Analyze if there are genre-specific patterns in low ratio songs."""
    
    # This would require genre metadata - for now, analyze musical characteristics
    genre_analysis = {
        "tempo_patterns": [],
        "complexity_patterns": [],
        "duration_patterns": []
    }
    
    for song_name in low_ratio_songs:
        try:
            candidates, features, stats = load_song_data(song_name)
            
            # Analyze tempo (from candidates per beat)
            candidates_per_beat = stats.get("candidates_per_beat", 0)
            genre_analysis["tempo_patterns"].append(candidates_per_beat)
            
            # Analyze complexity (from spectral features variance)
            spectral_bandwidths = [c["spectral_features"]["spectral_bandwidth"] for c in candidates]
            complexity = np.std(spectral_bandwidths) if spectral_bandwidths else 0
            genre_analysis["complexity_patterns"].append(complexity)
            
            # Analyze duration (from total candidates)
            duration_proxy = len(candidates)
            genre_analysis["duration_patterns"].append(duration_proxy)
            
        except Exception as e:
            print(f"Warning: Could not analyze {song_name}: {e}")
    
    return genre_analysis

def main():
    """Main analysis function."""
    
    print("🔍 ANALYZING LOW STRONG CANDIDATE RATIO PATTERNS")
    print("="*80)
    
    # Load processing report to identify low ratio songs
    report_path = Path("data/processed/phase6/candidate_detection_report.json")
    with open(report_path, 'r') as f:
        report = json.load(f)
    
    # Find songs with low strong candidate ratios
    low_ratio_songs = []
    all_songs_analysis = []
    
    for result in report["results"]:
        if result["status"] == "success":
            stats = result["detection_stats"]
            total = stats["total_candidates"]
            strong = stats["strong_candidates"]
            ratio = strong / total if total > 0 else 0
            
            song_data = {
                "song": result["song_name"],
                "ratio": ratio,
                "total": total,
                "strong": strong
            }
            all_songs_analysis.append(song_data)
            
            if ratio < 0.3:
                low_ratio_songs.append(result["song_name"])
    
    print(f"📊 Found {len(low_ratio_songs)} songs with strong ratio < 30%:")
    for song in low_ratio_songs:
        song_data = next(s for s in all_songs_analysis if s["song"] == song)
        print(f"  - {song}: {song_data['ratio']*100:.1f}% ({song_data['strong']}/{song_data['total']})")
    
    # Analyze each low ratio song in detail
    print(f"\n🔬 DETAILED ANALYSIS OF LOW RATIO SONGS:")
    all_recommendations = []
    
    for song_name in low_ratio_songs:
        print(f"\n--- Analyzing {song_name} ---")
        
        try:
            candidates, features, stats = load_song_data(song_name)
            analysis = analyze_confidence_components(candidates)
            contributions = calculate_component_contributions(analysis)
            bottlenecks = identify_bottlenecks(analysis, contributions)
            recommendations = recommend_parameter_adjustments(song_name, analysis, contributions, bottlenecks)
            
            print(f"Current strong ratio: {recommendations['current_strong_ratio']*100:.1f}%")
            print(f"Bottlenecks:")
            print(f"  - Onset limited: {bottlenecks['onset_limited']} candidates")
            print(f"  - Energy limited: {bottlenecks['energy_limited']} candidates")
            print(f"  - Spectral limited: {bottlenecks['spectral_limited']} candidates")
            print(f"  - Subdivision limited: {bottlenecks['subdivision_limited']} candidates")
            
            if recommendations["adjustments"]:
                print(f"Recommended adjustments:")
                for adj in recommendations["adjustments"]:
                    print(f"  - {adj['parameter']}: {adj['current']} → {adj['recommended']} ({adj['reason']})")
            else:
                print("  - No specific parameter adjustments recommended")
            
            all_recommendations.append(recommendations)
            
        except Exception as e:
            print(f"  ❌ Error analyzing {song_name}: {e}")
    
    # Analyze genre patterns
    print(f"\n🎵 GENRE PATTERN ANALYSIS:")
    genre_analysis = analyze_genre_patterns(low_ratio_songs)
    
    if genre_analysis["tempo_patterns"]:
        tempo_mean = np.mean(genre_analysis["tempo_patterns"])
        tempo_std = np.std(genre_analysis["tempo_patterns"])
        print(f"Tempo patterns (candidates/beat): {tempo_mean:.2f} ± {tempo_std:.2f}")
    
    if genre_analysis["complexity_patterns"]:
        complexity_mean = np.mean(genre_analysis["complexity_patterns"])
        complexity_std = np.std(genre_analysis["complexity_patterns"])
        print(f"Spectral complexity: {complexity_mean:.1f} ± {complexity_std:.1f}")
    
    # Generate overall recommendations
    print(f"\n💡 OVERALL RECOMMENDATIONS:")
    
    # Count parameter adjustment frequencies
    param_counts = {}
    for rec in all_recommendations:
        for adj in rec["adjustments"]:
            param = adj["parameter"]
            param_counts[param] = param_counts.get(param, 0) + 1
    
    if param_counts:
        print("Most frequently recommended adjustments:")
        for param, count in sorted(param_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / len(all_recommendations) * 100
            print(f"  - {param}: {count}/{len(all_recommendations)} songs ({percentage:.1f}%)")
    
    # Final recommendations
    print(f"\n🎯 IMPLEMENTATION RECOMMENDATIONS:")
    
    if len(low_ratio_songs) <= 2:
        print("✅ Current parameters are working well for most songs")
        print("   - Consider song-specific adjustments for outliers")
        print("   - Monitor performance on full dataset")
    else:
        print("🔧 Consider global parameter adjustments:")
        if param_counts.get("onset_scaling", 0) >= 2:
            print("   - Increase onset scaling from 0.5 to 0.6")
        if param_counts.get("spectral_divisor", 0) >= 2:
            print("   - Decrease spectral divisor from 1500 to 1200")
        if param_counts.get("subdivision_penalties", 0) >= 2:
            print("   - Reduce subdivision penalties (increase weights)")
    
    print(f"\n📊 SUMMARY:")
    print(f"   - Total songs analyzed: {len(all_songs_analysis)}")
    print(f"   - Songs with low ratios: {len(low_ratio_songs)} ({len(low_ratio_songs)/len(all_songs_analysis)*100:.1f}%)")
    print(f"   - Average strong ratio: {np.mean([s['ratio'] for s in all_songs_analysis])*100:.1f}%")
    print(f"   - System performance: {'Excellent' if len(low_ratio_songs) <= 2 else 'Good - needs tuning'}")

if __name__ == "__main__":
    main()
