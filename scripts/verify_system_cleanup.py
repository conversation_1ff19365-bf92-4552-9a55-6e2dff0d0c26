#!/usr/bin/env python3
"""
Verify complete system cleanup and validate production-ready state.

This script performs comprehensive verification that all test files and
temporary data have been removed, leaving only production-ready files.

Author: TJAGen System Verification
Version: 1.0.0
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Set

def get_expected_production_songs() -> Set[str]:
    """Get the expected set of production songs."""
    return {
        "Ai Uta", "ABC", "1 2 3", "1 Dream", "ADAMAS", "ALIVE", 
        "Angel Dream", "Anpanman no March -New Audio-", "Ao no Sumika", 
        "Ashita no Hikari", "142 Toki no Watari-dori"
    }

def verify_directory_structure(phase6_dir: Path) -> Dict:
    """Verify the Phase 6 directory structure is correct."""
    
    expected_dirs = [
        "note_candidates",
        "candidate_features", 
        "detection_stats",
        "candidate_audio",
        "logs"
    ]
    
    verification = {
        "missing_dirs": [],
        "extra_dirs": [],
        "correct_dirs": []
    }
    
    actual_dirs = [d.name for d in phase6_dir.iterdir() if d.is_dir()]
    
    for expected_dir in expected_dirs:
        if expected_dir in actual_dirs:
            verification["correct_dirs"].append(expected_dir)
        else:
            verification["missing_dirs"].append(expected_dir)
    
    for actual_dir in actual_dirs:
        if actual_dir not in expected_dirs and not actual_dir.startswith('.'):
            verification["extra_dirs"].append(actual_dir)
    
    return verification

def verify_json_files(phase6_dir: Path, production_songs: Set[str]) -> Dict:
    """Verify JSON files contain only production songs."""
    
    json_dirs = ["note_candidates", "candidate_features", "detection_stats"]
    verification = {}
    
    for json_dir_name in json_dirs:
        json_dir = phase6_dir / json_dir_name
        if not json_dir.exists():
            verification[json_dir_name] = {"status": "missing_directory"}
            continue
        
        json_files = list(json_dir.glob("*.json"))
        
        # Extract song names from files
        found_songs = set()
        for json_file in json_files:
            song_name = json_file.stem
            # Remove suffixes
            for suffix in ["_candidates", "_features", "_stats"]:
                if song_name.endswith(suffix):
                    song_name = song_name[:-len(suffix)]
                    break
            found_songs.add(song_name)
        
        verification[json_dir_name] = {
            "status": "verified",
            "expected_songs": len(production_songs),
            "found_songs": len(found_songs),
            "missing_songs": production_songs - found_songs,
            "extra_songs": found_songs - production_songs,
            "total_files": len(json_files)
        }
    
    return verification

def verify_audio_files(phase6_dir: Path, production_songs: Set[str]) -> Dict:
    """Verify audio files contain only production songs."""
    
    audio_dir = phase6_dir / "candidate_audio"
    if not audio_dir.exists():
        return {"status": "missing_directory"}
    
    audio_files = list(audio_dir.glob("*.npy"))
    
    # Extract song names from audio files
    found_songs = set()
    for audio_file in audio_files:
        # Audio files are named like "Song Name_123.npy"
        song_name = audio_file.stem.rsplit('_', 1)[0]
        found_songs.add(song_name)
    
    return {
        "status": "verified",
        "expected_songs": len(production_songs),
        "found_songs": len(found_songs),
        "missing_songs": production_songs - found_songs,
        "extra_songs": found_songs - production_songs,
        "total_files": len(audio_files)
    }

def verify_logs(phase6_dir: Path) -> Dict:
    """Verify log files are properly managed."""
    
    logs_dir = phase6_dir / "logs"
    if not logs_dir.exists():
        return {"status": "missing_directory"}
    
    log_files = list(logs_dir.glob("*.log"))
    
    return {
        "status": "verified",
        "total_logs": len(log_files),
        "log_files": [f.name for f in log_files],
        "recommendation": "Keep only 1-2 most recent logs" if len(log_files) > 2 else "Optimal"
    }

def verify_processing_report(phase6_dir: Path, production_songs: Set[str]) -> Dict:
    """Verify the processing report contains only production songs."""
    
    report_file = phase6_dir / "candidate_detection_report.json"
    if not report_file.exists():
        return {"status": "missing_report"}
    
    with open(report_file, 'r') as f:
        report = json.load(f)
    
    # Extract songs from report
    report_songs = set()
    for result in report.get("results", []):
        report_songs.add(result.get("song_name", ""))
    
    return {
        "status": "verified",
        "expected_songs": len(production_songs),
        "found_songs": len(report_songs),
        "missing_songs": production_songs - report_songs,
        "extra_songs": report_songs - production_songs,
        "total_results": len(report.get("results", []))
    }

def check_for_temporary_files(phase6_dir: Path) -> Dict:
    """Check for any temporary or test files that should be removed."""
    
    temp_patterns = [
        "*.tmp", "*.temp", "*_test*", "*_debug*", 
        "*.bak", "*.old", "*_backup*"
    ]
    
    temp_files = []
    for pattern in temp_patterns:
        temp_files.extend(phase6_dir.rglob(pattern))
    
    return {
        "temp_files_found": len(temp_files),
        "temp_files": [str(f.relative_to(phase6_dir)) for f in temp_files],
        "status": "clean" if len(temp_files) == 0 else "needs_cleanup"
    }

def calculate_storage_usage(phase6_dir: Path) -> Dict:
    """Calculate storage usage of the Phase 6 directory."""
    
    total_size = 0
    file_counts = {}
    
    for file_path in phase6_dir.rglob("*"):
        if file_path.is_file():
            size = file_path.stat().st_size
            total_size += size
            
            ext = file_path.suffix.lower()
            if ext not in file_counts:
                file_counts[ext] = {"count": 0, "size": 0}
            file_counts[ext]["count"] += 1
            file_counts[ext]["size"] += size
    
    return {
        "total_size_mb": total_size / (1024 * 1024),
        "total_files": sum(fc["count"] for fc in file_counts.values()),
        "file_breakdown": file_counts
    }

def main():
    """Main verification function."""
    
    print("🔍 VERIFYING COMPLETE SYSTEM CLEANUP")
    print("="*80)
    
    phase6_dir = Path("data/processed/phase6")
    if not phase6_dir.exists():
        print("❌ Phase 6 directory not found!")
        return 1
    
    production_songs = get_expected_production_songs()
    print(f"📋 Expected production songs: {len(production_songs)}")
    
    # Verify directory structure
    print(f"\n📁 DIRECTORY STRUCTURE VERIFICATION:")
    dir_verification = verify_directory_structure(phase6_dir)
    
    if dir_verification["missing_dirs"]:
        print(f"❌ Missing directories: {dir_verification['missing_dirs']}")
    if dir_verification["extra_dirs"]:
        print(f"⚠️  Extra directories: {dir_verification['extra_dirs']}")
    if dir_verification["correct_dirs"]:
        print(f"✅ Correct directories: {dir_verification['correct_dirs']}")
    
    # Verify JSON files
    print(f"\n📄 JSON FILES VERIFICATION:")
    json_verification = verify_json_files(phase6_dir, production_songs)
    
    for dir_name, verification in json_verification.items():
        if verification["status"] == "missing_directory":
            print(f"❌ {dir_name}: Directory missing")
        else:
            missing = len(verification["missing_songs"])
            extra = len(verification["extra_songs"])
            if missing == 0 and extra == 0:
                print(f"✅ {dir_name}: {verification['total_files']} files for {verification['found_songs']} songs")
            else:
                print(f"⚠️  {dir_name}: {missing} missing, {extra} extra songs")
    
    # Verify audio files
    print(f"\n🎵 AUDIO FILES VERIFICATION:")
    audio_verification = verify_audio_files(phase6_dir, production_songs)
    
    if audio_verification["status"] == "missing_directory":
        print(f"❌ candidate_audio: Directory missing")
    else:
        missing = len(audio_verification["missing_songs"])
        extra = len(audio_verification["extra_songs"])
        if missing == 0 and extra == 0:
            print(f"✅ candidate_audio: {audio_verification['total_files']} files for {audio_verification['found_songs']} songs")
        else:
            print(f"⚠️  candidate_audio: {missing} missing, {extra} extra songs")
    
    # Verify logs
    print(f"\n📝 LOG FILES VERIFICATION:")
    log_verification = verify_logs(phase6_dir)
    
    if log_verification["status"] == "missing_directory":
        print(f"❌ logs: Directory missing")
    else:
        print(f"✅ logs: {log_verification['total_logs']} log files")
        print(f"   Recommendation: {log_verification['recommendation']}")
    
    # Verify processing report
    print(f"\n📊 PROCESSING REPORT VERIFICATION:")
    report_verification = verify_processing_report(phase6_dir, production_songs)
    
    if report_verification["status"] == "missing_report":
        print(f"❌ Processing report missing")
    else:
        missing = len(report_verification["missing_songs"])
        extra = len(report_verification["extra_songs"])
        if missing == 0 and extra == 0:
            print(f"✅ Processing report: {report_verification['total_results']} results for {report_verification['found_songs']} songs")
        else:
            print(f"⚠️  Processing report: {missing} missing, {extra} extra songs")
    
    # Check for temporary files
    print(f"\n🧹 TEMPORARY FILES CHECK:")
    temp_verification = check_for_temporary_files(phase6_dir)
    
    if temp_verification["status"] == "clean":
        print(f"✅ No temporary files found")
    else:
        print(f"⚠️  Found {temp_verification['temp_files_found']} temporary files:")
        for temp_file in temp_verification["temp_files"]:
            print(f"   - {temp_file}")
    
    # Calculate storage usage
    print(f"\n💾 STORAGE USAGE ANALYSIS:")
    storage_info = calculate_storage_usage(phase6_dir)
    
    print(f"Total size: {storage_info['total_size_mb']:.1f} MB")
    print(f"Total files: {storage_info['total_files']}")
    print(f"File breakdown:")
    for ext, info in sorted(storage_info["file_breakdown"].items(), key=lambda x: x[1]["size"], reverse=True):
        if info["count"] > 0:
            print(f"   {ext or 'no extension'}: {info['count']} files, {info['size']/(1024*1024):.1f} MB")
    
    # Overall assessment
    print(f"\n🎯 OVERALL SYSTEM STATUS:")
    
    issues = []
    if dir_verification["missing_dirs"]:
        issues.append("Missing directories")
    if any(v.get("status") == "missing_directory" for v in json_verification.values()):
        issues.append("Missing JSON directories")
    if audio_verification.get("status") == "missing_directory":
        issues.append("Missing audio directory")
    if temp_verification["status"] != "clean":
        issues.append("Temporary files present")
    
    if not issues:
        print(f"✅ SYSTEM STATUS: PRODUCTION READY")
        print(f"   - All directories present and correct")
        print(f"   - All production songs accounted for")
        print(f"   - No temporary files found")
        print(f"   - Storage usage optimized")
        return 0
    else:
        print(f"⚠️  SYSTEM STATUS: NEEDS ATTENTION")
        print(f"   Issues found:")
        for issue in issues:
            print(f"   - {issue}")
        return 1

if __name__ == "__main__":
    exit(main())
