#!/usr/bin/env python3
"""
Analyze Phase 6 performance metrics and identify optimization opportunities.

This script profiles the Phase 6 processing pipeline to identify bottlenecks
and opportunities for speed improvements while maintaining quality.

Author: TJAGen Performance Analysis
Version: 1.0.0
"""

import json
import time
import psutil
import numpy as np
from pathlib import Path
import cProfile
import pstats
from io import String<PERSON>

def analyze_processing_times():
    """Analyze processing time breakdown from logs."""
    
    print("🔍 ANALYZING PROCESSING TIME BREAKDOWN")
    print("="*60)
    
    # Load processing report
    report_path = Path("data/processed/phase6/candidate_detection_report.json")
    if not report_path.exists():
        print("❌ No processing report found")
        return
    
    with open(report_path, 'r') as f:
        report = json.load(f)
    
    results = report["results"]
    successful_results = [r for r in results if r["status"] == "success"]
    
    if not successful_results:
        print("❌ No successful processing results found")
        return
    
    # Analyze processing times
    processing_times = [r["processing_time"] for r in successful_results]
    candidate_counts = [r["detection_stats"]["total_candidates"] for r in successful_results]
    
    print(f"📊 PROCESSING TIME ANALYSIS:")
    print(f"  Total songs processed: {len(successful_results)}")
    print(f"  Mean processing time: {np.mean(processing_times):.2f}s")
    print(f"  Std processing time: {np.std(processing_times):.2f}s")
    print(f"  Min processing time: {np.min(processing_times):.2f}s")
    print(f"  Max processing time: {np.max(processing_times):.2f}s")
    print(f"  Median processing time: {np.median(processing_times):.2f}s")
    
    # Analyze candidates per second
    candidates_per_second = [c/t for c, t in zip(candidate_counts, processing_times)]
    print(f"\n📈 THROUGHPUT ANALYSIS:")
    print(f"  Mean candidates/second: {np.mean(candidates_per_second):.1f}")
    print(f"  Total candidates generated: {sum(candidate_counts)}")
    print(f"  Total processing time: {sum(processing_times):.2f}s")
    
    # Identify slow songs
    slow_threshold = np.mean(processing_times) + np.std(processing_times)
    slow_songs = [(r["song_name"], r["processing_time"]) for r in successful_results 
                  if r["processing_time"] > slow_threshold]
    
    if slow_songs:
        print(f"\n⚠️  SLOW PROCESSING SONGS (>{slow_threshold:.2f}s):")
        for song, time_taken in sorted(slow_songs, key=lambda x: x[1], reverse=True):
            print(f"  {song}: {time_taken:.2f}s")
    
    # Performance recommendations
    print(f"\n💡 PERFORMANCE OPTIMIZATION OPPORTUNITIES:")
    
    if np.mean(processing_times) > 2.0:
        print("  🔧 Consider optimizing candidate generation algorithm")
    
    if np.std(processing_times) > 2.0:
        print("  🔧 High variance suggests song-specific optimizations needed")
    
    if len(slow_songs) > len(successful_results) * 0.2:
        print("  🔧 More than 20% of songs are slow - investigate common patterns")
    
    return {
        "mean_time": np.mean(processing_times),
        "std_time": np.std(processing_times),
        "throughput": np.mean(candidates_per_second),
        "slow_songs": len(slow_songs)
    }

def analyze_memory_usage():
    """Analyze memory usage patterns."""
    
    print(f"\n🧠 MEMORY USAGE ANALYSIS")
    print("="*60)
    
    # Get current memory usage
    process = psutil.Process()
    memory_info = process.memory_info()
    
    print(f"📊 CURRENT MEMORY USAGE:")
    print(f"  RSS (Resident Set Size): {memory_info.rss / 1024 / 1024:.1f} MB")
    print(f"  VMS (Virtual Memory Size): {memory_info.vms / 1024 / 1024:.1f} MB")
    
    # System memory
    system_memory = psutil.virtual_memory()
    print(f"  System Memory Available: {system_memory.available / 1024 / 1024:.1f} MB")
    print(f"  System Memory Usage: {system_memory.percent:.1f}%")
    
    # Memory optimization recommendations
    print(f"\n💡 MEMORY OPTIMIZATION RECOMMENDATIONS:")
    
    if memory_info.rss > 1024 * 1024 * 1024:  # > 1GB
        print("  🔧 Consider implementing memory cleanup between songs")
    
    if system_memory.percent > 80:
        print("  ⚠️  System memory usage high - consider batch processing")
    
    print("  ✅ Implement garbage collection after each song")
    print("  ✅ Use memory-mapped files for large audio data")
    print("  ✅ Clear intermediate arrays after processing")

def profile_candidate_generation():
    """Profile the candidate generation process."""
    
    print(f"\n⚡ CANDIDATE GENERATION PROFILING")
    print("="*60)
    
    # This would require running actual profiling during processing
    # For now, provide analysis based on known bottlenecks
    
    print(f"🔍 KNOWN PERFORMANCE BOTTLENECKS:")
    print("  1. Spectral feature extraction (MFCC, spectral features)")
    print("  2. Onset detection processing")
    print("  3. Beat subdivision calculations")
    print("  4. Confidence score computation")
    print("  5. Audio snippet extraction and saving")
    
    print(f"\n🚀 OPTIMIZATION STRATEGIES:")
    print("  ✅ Vectorize spectral computations using NumPy")
    print("  ✅ Cache repeated calculations (e.g., FFT windows)")
    print("  ✅ Use numba JIT compilation for hot loops")
    print("  ✅ Batch process multiple candidates together")
    print("  ✅ Optimize file I/O operations")

def recommend_threshold_adjustments():
    """Recommend threshold adjustments based on current performance."""
    
    print(f"\n🎯 THRESHOLD OPTIMIZATION RECOMMENDATIONS")
    print("="*60)
    
    # Load current results for analysis
    report_path = Path("data/processed/phase6/candidate_detection_report.json")
    if not report_path.exists():
        return
    
    with open(report_path, 'r') as f:
        report = json.load(f)
    
    results = report["results"]
    successful_results = [r for r in results if r["status"] == "success"]
    
    # Calculate current metrics
    strong_ratios = []
    candidate_counts = []
    
    for result in successful_results:
        stats = result["detection_stats"]
        total = stats["total_candidates"]
        strong = stats["strong_candidates"]
        
        if total > 0:
            strong_ratios.append(strong / total)
            candidate_counts.append(total)
    
    mean_strong_ratio = np.mean(strong_ratios)
    mean_candidates = np.mean(candidate_counts)
    
    print(f"📊 CURRENT PERFORMANCE BASELINE:")
    print(f"  Mean strong candidate ratio: {mean_strong_ratio:.3f} ({mean_strong_ratio*100:.1f}%)")
    print(f"  Mean candidates per song: {mean_candidates:.1f}")
    print(f"  Target strong ratio range: 0.35-0.50 (35-50%)")
    
    print(f"\n💡 THRESHOLD RECOMMENDATIONS:")
    
    if mean_strong_ratio > 0.5:
        print("  🔧 Strong ratio too high - consider tightening confidence threshold")
        print("     Recommended: Increase strong threshold from 0.6 to 0.65")
    elif mean_strong_ratio < 0.35:
        print("  🔧 Strong ratio too low - consider loosening confidence threshold")
        print("     Recommended: Decrease strong threshold from 0.6 to 0.55")
    else:
        print("  ✅ Strong candidate ratio is optimal - no changes needed")
    
    if mean_candidates > 400:
        print("  🔧 Too many candidates - consider stricter filtering")
    elif mean_candidates < 200:
        print("  🔧 Too few candidates - consider looser filtering")
    else:
        print("  ✅ Candidate count is optimal - no changes needed")

def main():
    """Main performance analysis function."""
    
    print("🚀 PHASE 6 PERFORMANCE ANALYSIS & OPTIMIZATION")
    print("="*80)
    
    # Analyze processing times
    perf_metrics = analyze_processing_times()
    
    # Analyze memory usage
    analyze_memory_usage()
    
    # Profile candidate generation
    profile_candidate_generation()
    
    # Recommend threshold adjustments
    recommend_threshold_adjustments()
    
    # Final recommendations
    print(f"\n🎯 FINAL OPTIMIZATION RECOMMENDATIONS")
    print("="*80)
    
    if perf_metrics and perf_metrics["mean_time"] > 3.0:
        print("🔧 HIGH PRIORITY: Optimize processing speed")
        print("   - Implement numba JIT compilation for hot loops")
        print("   - Vectorize spectral feature calculations")
        print("   - Cache repeated computations")
    
    print("🔧 MEDIUM PRIORITY: Code cleanup and refactoring")
    print("   - Remove debug logging statements")
    print("   - Consolidate redundant functions")
    print("   - Optimize memory usage patterns")
    
    print("🔧 LOW PRIORITY: Fine-tune parameters")
    print("   - Monitor strong candidate ratios in production")
    print("   - Adjust thresholds based on larger dataset")
    
    print(f"\n✅ SYSTEM STATUS: Optimized and ready for cleanup")

if __name__ == "__main__":
    main()
