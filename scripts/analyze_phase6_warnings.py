#!/usr/bin/env python3
"""
Analyze Phase 6 warnings and candidate quality metrics.

This script analyzes the Phase 6 processing results to understand:
1. Low strong candidate ratio patterns
2. Too few candidates issues
3. Overall quality metrics and recommendations

Author: TJAGen Pipeline Analysis
Version: 1.0.0
"""

import json
import sys
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt

def load_processing_report(report_path):
    """Load the Phase 6 processing report."""
    with open(report_path, 'r') as f:
        return json.load(f)

def analyze_candidate_quality(results):
    """Analyze candidate quality metrics from processing results."""
    
    analysis = {
        "total_songs": len(results),
        "low_strong_ratio_songs": [],
        "too_few_candidates_songs": [],
        "quality_metrics": {
            "strong_ratios": [],
            "candidate_counts": [],
            "candidates_per_beat": [],
            "false_positive_estimates": []
        }
    }
    
    # Thresholds from the implementation
    MIN_CANDIDATES = 50
    MIN_STRONG_RATIO = 0.3
    
    for result in results:
        if result["status"] != "success":
            continue
            
        stats = result["detection_stats"]
        song_name = result["song_name"]
        
        # Calculate strong candidate ratio
        total_candidates = stats["total_candidates"]
        strong_candidates = stats["strong_candidates"]
        strong_ratio = strong_candidates / total_candidates if total_candidates > 0 else 0
        
        # Collect metrics
        analysis["quality_metrics"]["strong_ratios"].append(strong_ratio)
        analysis["quality_metrics"]["candidate_counts"].append(total_candidates)
        analysis["quality_metrics"]["candidates_per_beat"].append(stats["candidates_per_beat"])
        analysis["quality_metrics"]["false_positive_estimates"].append(stats["false_positive_estimate"])
        
        # Check for warnings
        if strong_ratio < MIN_STRONG_RATIO:
            analysis["low_strong_ratio_songs"].append({
                "song": song_name,
                "strong_ratio": strong_ratio,
                "total_candidates": total_candidates,
                "strong_candidates": strong_candidates,
                "candidates_per_beat": stats["candidates_per_beat"]
            })
        
        if total_candidates < MIN_CANDIDATES:
            analysis["too_few_candidates_songs"].append({
                "song": song_name,
                "total_candidates": total_candidates,
                "candidates_per_beat": stats["candidates_per_beat"],
                "strong_ratio": strong_ratio
            })
    
    return analysis

def print_analysis_report(analysis):
    """Print detailed analysis report."""
    
    print("="*80)
    print("PHASE 6 WARNING ANALYSIS REPORT")
    print("="*80)
    
    total_songs = analysis["total_songs"]
    low_strong_count = len(analysis["low_strong_ratio_songs"])
    too_few_count = len(analysis["too_few_candidates_songs"])
    
    print(f"\n📊 OVERVIEW:")
    print(f"Total songs analyzed: {total_songs}")
    print(f"Songs with low strong candidate ratio (<30%): {low_strong_count} ({low_strong_count/total_songs*100:.1f}%)")
    print(f"Songs with too few candidates (<50): {too_few_count} ({too_few_count/total_songs*100:.1f}%)")
    
    # Quality metrics summary
    metrics = analysis["quality_metrics"]
    print(f"\n📈 QUALITY METRICS SUMMARY:")
    print(f"Strong candidate ratio - Mean: {np.mean(metrics['strong_ratios']):.3f}, Std: {np.std(metrics['strong_ratios']):.3f}")
    print(f"Candidate count - Mean: {np.mean(metrics['candidate_counts']):.1f}, Std: {np.std(metrics['candidate_counts']):.1f}")
    print(f"Candidates per beat - Mean: {np.mean(metrics['candidates_per_beat']):.2f}, Std: {np.std(metrics['candidates_per_beat']):.2f}")
    print(f"False positive estimate - Mean: {np.mean(metrics['false_positive_estimates']):.3f}, Std: {np.std(metrics['false_positive_estimates']):.3f}")
    
    # Low strong ratio analysis
    if analysis["low_strong_ratio_songs"]:
        print(f"\n⚠️  LOW STRONG CANDIDATE RATIO SONGS:")
        print(f"{'Song':<30} {'Strong%':<8} {'Total':<6} {'Strong':<7} {'Per Beat':<8}")
        print("-" * 65)
        for song_data in analysis["low_strong_ratio_songs"]:
            print(f"{song_data['song']:<30} {song_data['strong_ratio']*100:>6.1f}% {song_data['total_candidates']:>5} {song_data['strong_candidates']:>6} {song_data['candidates_per_beat']:>7.2f}")
    
    # Too few candidates analysis
    if analysis["too_few_candidates_songs"]:
        print(f"\n⚠️  TOO FEW CANDIDATES SONGS:")
        print(f"{'Song':<30} {'Total':<6} {'Per Beat':<8} {'Strong%':<8}")
        print("-" * 55)
        for song_data in analysis["too_few_candidates_songs"]:
            print(f"{song_data['song']:<30} {song_data['total_candidates']:>5} {song_data['candidates_per_beat']:>7.2f} {song_data['strong_ratio']*100:>6.1f}%")

def investigate_input_data_quality(song_name):
    """Investigate input data quality for a specific song."""
    
    print(f"\n🔍 INVESTIGATING INPUT DATA FOR: {song_name}")
    
    # Check Phase 5 data
    phase5_dir = Path("data/processed/phase5")
    beats_file = phase5_dir / "aligned_beats" / f"{song_name}.json"
    bpm_file = phase5_dir / "bpm_validation" / f"{song_name}.json"
    
    if beats_file.exists():
        with open(beats_file, 'r') as f:
            beats_data = json.load(f)
        print(f"  Phase 5 aligned beats: {len(beats_data)} beats found")
        
        if len(beats_data) > 1:
            beat_times = [beat["beat_time"] for beat in beats_data]
            intervals = np.diff(beat_times)
            print(f"  Beat intervals - Mean: {np.mean(intervals):.3f}s, Std: {np.std(intervals):.3f}s")
    else:
        print(f"  ❌ Phase 5 beats file missing: {beats_file}")
    
    if bpm_file.exists():
        with open(bpm_file, 'r') as f:
            bpm_data = json.load(f)
        print(f"  BPM validation: {bpm_data.get('detected_bpm', 'N/A')} BPM")
    else:
        print(f"  ❌ BPM validation file missing: {bpm_file}")
    
    # Check Phase 4 data
    phase4_dir = Path("data/processed/phase4/outputs")
    onset_files = list((phase4_dir / "onset_positions").glob(f"{song_name}_segment_*.json"))
    beat_files = list((phase4_dir / "beat_positions").glob(f"{song_name}_segment_*.json"))
    
    print(f"  Phase 4 onset files: {len(onset_files)} segments")
    print(f"  Phase 4 beat files: {len(beat_files)} segments")
    
    # Check Phase 3 data
    phase3_dir = Path("data/processed/phase3")
    segments_file = phase3_dir / "audio_segments" / f"{song_name}_segments.json"
    
    if segments_file.exists():
        with open(segments_file, 'r') as f:
            segments_data = json.load(f)
        print(f"  Phase 3 audio segments: {len(segments_data)} segments")
        
        total_duration = sum(seg.get("duration", 0) for seg in segments_data)
        print(f"  Total audio duration: {total_duration:.2f} seconds")
    else:
        print(f"  ❌ Phase 3 segments file missing: {segments_file}")

def main():
    """Main analysis function."""
    
    # Load processing report
    report_path = Path("data/processed/phase6/candidate_detection_report.json")
    
    if not report_path.exists():
        print(f"Error: Processing report not found at {report_path}")
        return 1
    
    report = load_processing_report(report_path)
    results = report["results"]
    
    # Analyze candidate quality
    analysis = analyze_candidate_quality(results)
    
    # Print analysis report
    print_analysis_report(analysis)
    
    # Investigate specific problematic songs
    print(f"\n🔍 DETAILED INVESTIGATION:")
    
    # Investigate songs with issues
    problem_songs = set()
    
    for song_data in analysis["low_strong_ratio_songs"][:3]:  # Top 3 worst
        problem_songs.add(song_data["song"])
    
    for song_data in analysis["too_few_candidates_songs"][:3]:  # Top 3 worst
        problem_songs.add(song_data["song"])
    
    for song_name in list(problem_songs)[:3]:  # Limit to 3 investigations
        investigate_input_data_quality(song_name)
    
    # Recommendations
    print(f"\n💡 RECOMMENDATIONS:")
    
    low_ratio_percentage = len(analysis["low_strong_ratio_songs"]) / analysis["total_songs"] * 100
    too_few_percentage = len(analysis["too_few_candidates_songs"]) / analysis["total_songs"] * 100
    
    if low_ratio_percentage > 50:
        print("  🔧 HIGH PRIORITY: Adjust strong candidate classification criteria")
        print("     - Consider lowering confidence threshold from 0.8 to 0.6-0.7")
        print("     - Review onset strength weighting in confidence calculation")
    elif low_ratio_percentage > 20:
        print("  ⚠️  MEDIUM PRIORITY: Monitor strong candidate ratios")
        print("     - Current thresholds may be too strict for some music genres")
    else:
        print("  ✅ LOW PRIORITY: Strong candidate ratios within acceptable range")
    
    if too_few_percentage > 20:
        print("  🔧 HIGH PRIORITY: Address candidate generation issues")
        print("     - Investigate input data quality from Phases 3-5")
        print("     - Consider lowering minimum candidate threshold")
    elif too_few_percentage > 10:
        print("  ⚠️  MEDIUM PRIORITY: Some songs have insufficient candidates")
        print("     - Review subdivision level parameters")
    else:
        print("  ✅ LOW PRIORITY: Candidate counts generally adequate")
    
    avg_false_positive = np.mean(analysis["quality_metrics"]["false_positive_estimates"])
    if avg_false_positive > 0.4:
        print("  ⚠️  Consider tightening confidence thresholds to reduce false positives")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
