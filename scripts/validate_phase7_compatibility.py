#!/usr/bin/env python3
"""
Validate Phase 6 output format compatibility with Phase 7 requirements.

This script verifies that all Phase 6 outputs match the exact format
specifications required for Phase 7 ML model training.

Author: TJAGen Format Validation
Version: 1.0.0
"""

import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Any

def validate_candidate_json_schema(candidate_data: List[Dict]) -> Dict:
    """Validate candidate JSON schema compliance."""
    
    validation = {
        "status": "valid",
        "issues": [],
        "candidate_count": len(candidate_data),
        "required_fields": [
            "candidate_id", "window_start_time", "window_end_time", "window_duration",
            "beat_alignment", "onset_strength", "spectral_features", "energy_profile",
            "beat_subdivision", "candidate_confidence", "candidate_type"
        ]
    }
    
    if not candidate_data:
        validation["status"] = "invalid"
        validation["issues"].append("No candidates found")
        return validation
    
    # Check first candidate for schema compliance
    sample_candidate = candidate_data[0]
    
    for field in validation["required_fields"]:
        if field not in sample_candidate:
            validation["status"] = "invalid"
            validation["issues"].append(f"Missing required field: {field}")
    
    # Validate specific field types and structures
    if "spectral_features" in sample_candidate:
        spectral = sample_candidate["spectral_features"]
        required_spectral = [
            "spectral_centroid", "spectral_bandwidth", "spectral_rolloff",
            "zero_crossing_rate", "mfcc"
        ]
        for spec_field in required_spectral:
            if spec_field not in spectral:
                validation["status"] = "invalid"
                validation["issues"].append(f"Missing spectral feature: {spec_field}")
    
    # Validate MFCC array structure
    if "mfcc" in sample_candidate.get("spectral_features", {}):
        mfcc = sample_candidate["spectral_features"]["mfcc"]
        if not isinstance(mfcc, list) or len(mfcc) != 13:
            validation["status"] = "invalid"
            validation["issues"].append(f"Invalid MFCC structure: expected 13 coefficients, got {len(mfcc) if isinstance(mfcc, list) else 'non-list'}")
    
    # Validate energy profile
    if "energy_profile" in sample_candidate:
        energy = sample_candidate["energy_profile"]
        if not isinstance(energy, list) or len(energy) == 0:
            validation["status"] = "invalid"
            validation["issues"].append("Invalid energy profile: must be non-empty list")
    
    return validation

def validate_features_json_schema(features_data: List[Dict]) -> Dict:
    """Validate features JSON schema compliance."""

    validation = {
        "status": "valid",
        "issues": [],
        "feature_count": len(features_data),
        "required_fields": ["window_id", "temporal_features", "spectral_features"]
    }

    if not features_data:
        validation["status"] = "invalid"
        validation["issues"].append("No features found")
        return validation

    # Check first feature for schema compliance
    sample_feature = features_data[0]

    for field in validation["required_fields"]:
        if field not in sample_feature:
            validation["status"] = "invalid"
            validation["issues"].append(f"Missing required field: {field}")

    # Validate temporal features structure
    if "temporal_features" in sample_feature:
        temporal = sample_feature["temporal_features"]
        required_temporal = ["duration", "beat_position", "onset_strength", "energy_max"]
        for temp_field in required_temporal:
            if temp_field not in temporal:
                validation["status"] = "invalid"
                validation["issues"].append(f"Missing temporal feature: {temp_field}")

    # Validate spectral features structure
    if "spectral_features" in sample_feature:
        spectral = sample_feature["spectral_features"]
        required_spectral = ["spectral_centroid", "spectral_bandwidth", "spectral_rolloff", "zero_crossing_rate", "mfcc_mean"]
        for spec_field in required_spectral:
            if spec_field not in spectral:
                validation["status"] = "invalid"
                validation["issues"].append(f"Missing spectral feature: {spec_field}")

    return validation

def validate_stats_json_schema(stats_data: Dict) -> Dict:
    """Validate stats JSON schema compliance."""
    
    validation = {
        "status": "valid",
        "issues": [],
        "required_fields": [
            "total_candidates", "strong_candidates", "weak_candidates", 
            "subdivision_candidates", "candidates_per_beat", "processing_time"
        ]
    }
    
    for field in validation["required_fields"]:
        if field not in stats_data:
            validation["status"] = "invalid"
            validation["issues"].append(f"Missing required field: {field}")
    
    # Validate numeric consistency
    if all(f in stats_data for f in ["total_candidates", "strong_candidates", "weak_candidates", "subdivision_candidates"]):
        total = stats_data["total_candidates"]
        sum_types = stats_data["strong_candidates"] + stats_data["weak_candidates"] + stats_data["subdivision_candidates"]
        if total != sum_types:
            validation["status"] = "invalid"
            validation["issues"].append(f"Candidate count mismatch: total={total}, sum of types={sum_types}")
    
    return validation

def validate_audio_files(song_name: str, candidate_count: int) -> Dict:
    """Validate audio file presence and format."""
    
    validation = {
        "status": "valid",
        "issues": [],
        "expected_files": candidate_count,
        "found_files": 0,
        "sample_validated": False
    }
    
    audio_dir = Path("data/processed/phase6/candidate_audio")
    if not audio_dir.exists():
        validation["status"] = "invalid"
        validation["issues"].append("Audio directory missing")
        return validation
    
    # Find audio files for this song
    audio_files = list(audio_dir.glob(f"{song_name}_*.npy"))
    validation["found_files"] = len(audio_files)
    
    if len(audio_files) != candidate_count:
        validation["status"] = "invalid"
        validation["issues"].append(f"Audio file count mismatch: expected {candidate_count}, found {len(audio_files)}")
    
    # Validate sample audio file
    if audio_files:
        try:
            sample_audio = np.load(audio_files[0])
            if sample_audio.dtype != np.float32:
                validation["issues"].append(f"Invalid audio dtype: expected float32, got {sample_audio.dtype}")
            if len(sample_audio.shape) != 1:
                validation["issues"].append(f"Invalid audio shape: expected 1D array, got {sample_audio.shape}")
            validation["sample_validated"] = True
        except Exception as e:
            validation["status"] = "invalid"
            validation["issues"].append(f"Failed to load sample audio: {e}")
    
    return validation

def validate_cross_references(song_name: str) -> Dict:
    """Validate cross-references between files."""
    
    validation = {
        "status": "valid",
        "issues": [],
        "candidate_ids_consistent": False,
        "counts_consistent": False
    }
    
    try:
        # Load all files
        base_dir = Path("data/processed/phase6")
        
        with open(base_dir / "note_candidates" / f"{song_name}_candidates.json", 'r') as f:
            candidates = json.load(f)
        
        with open(base_dir / "candidate_features" / f"{song_name}_features.json", 'r') as f:
            features = json.load(f)
        
        with open(base_dir / "detection_stats" / f"{song_name}_stats.json", 'r') as f:
            stats = json.load(f)
        
        # Extract candidate IDs
        candidate_ids = {c["candidate_id"] for c in candidates}
        feature_ids = {f["window_id"] for f in features}

        # Validate ID consistency
        if candidate_ids == feature_ids:
            validation["candidate_ids_consistent"] = True
        else:
            validation["status"] = "invalid"
            validation["issues"].append(f"Candidate ID mismatch: {len(candidate_ids)} candidates, {len(feature_ids)} features")

        # Validate count consistency
        candidate_count = len(candidates)
        stats_count = stats.get("total_candidates", 0)
        feature_count = len(features)
        
        if candidate_count == stats_count == feature_count:
            validation["counts_consistent"] = True
        else:
            validation["status"] = "invalid"
            validation["issues"].append(f"Count mismatch: candidates={candidate_count}, stats={stats_count}, features={feature_count}")
    
    except Exception as e:
        validation["status"] = "invalid"
        validation["issues"].append(f"Failed to load files: {e}")
    
    return validation

def validate_phase7_readiness(song_name: str) -> Dict:
    """Comprehensive Phase 7 readiness validation."""
    
    print(f"🔍 Validating {song_name} for Phase 7 compatibility...")
    
    overall_validation = {
        "song_name": song_name,
        "phase7_ready": True,
        "validations": {},
        "summary": {
            "total_issues": 0,
            "critical_issues": 0,
            "warnings": 0
        }
    }
    
    try:
        # Load candidate data
        base_dir = Path("data/processed/phase6")
        with open(base_dir / "note_candidates" / f"{song_name}_candidates.json", 'r') as f:
            candidates = json.load(f)
        
        with open(base_dir / "candidate_features" / f"{song_name}_features.json", 'r') as f:
            features = json.load(f)
        
        with open(base_dir / "detection_stats" / f"{song_name}_stats.json", 'r') as f:
            stats = json.load(f)
        
        # Run all validations
        validations = {
            "candidates_schema": validate_candidate_json_schema(candidates),
            "features_schema": validate_features_json_schema(features),
            "stats_schema": validate_stats_json_schema(stats),
            "audio_files": validate_audio_files(song_name, len(candidates)),
            "cross_references": validate_cross_references(song_name)
        }
        
        overall_validation["validations"] = validations
        
        # Count issues
        for val_name, val_result in validations.items():
            if val_result["status"] != "valid":
                overall_validation["phase7_ready"] = False
            
            issue_count = len(val_result.get("issues", []))
            overall_validation["summary"]["total_issues"] += issue_count
            
            # Classify issues
            if val_result["status"] != "valid":
                overall_validation["summary"]["critical_issues"] += issue_count
        
        # Print results
        if overall_validation["phase7_ready"]:
            print(f"  ✅ {song_name}: Phase 7 ready")
        else:
            print(f"  ❌ {song_name}: Issues found")
            for val_name, val_result in validations.items():
                if val_result["status"] != "valid":
                    print(f"    - {val_name}: {len(val_result['issues'])} issues")
    
    except Exception as e:
        overall_validation["phase7_ready"] = False
        overall_validation["summary"]["critical_issues"] = 1
        print(f"  ❌ {song_name}: Validation failed - {e}")
    
    return overall_validation

def main():
    """Main validation function."""
    
    print("🔍 PHASE 7 COMPATIBILITY VALIDATION")
    print("="*80)
    
    # Get list of processed songs
    base_dir = Path("data/processed/phase6")
    candidates_dir = base_dir / "note_candidates"
    
    if not candidates_dir.exists():
        print("❌ No Phase 6 output found")
        return 1
    
    # Find all processed songs
    candidate_files = list(candidates_dir.glob("*_candidates.json"))
    songs = [f.stem.replace("_candidates", "") for f in candidate_files]
    
    print(f"📋 Found {len(songs)} processed songs")
    
    # Validate each song
    all_validations = []
    for song in songs:
        validation = validate_phase7_readiness(song)
        all_validations.append(validation)
    
    # Summary report
    print(f"\n📊 VALIDATION SUMMARY")
    print("="*80)
    
    ready_count = sum(1 for v in all_validations if v["phase7_ready"])
    total_issues = sum(v["summary"]["total_issues"] for v in all_validations)
    critical_issues = sum(v["summary"]["critical_issues"] for v in all_validations)
    
    print(f"Songs ready for Phase 7: {ready_count}/{len(songs)} ({ready_count/len(songs)*100:.1f}%)")
    print(f"Total issues found: {total_issues}")
    print(f"Critical issues: {critical_issues}")
    
    if ready_count == len(songs):
        print(f"\n✅ ALL SONGS PHASE 7 READY")
        print(f"   - Format compliance: Perfect")
        print(f"   - Schema validation: Passed")
        print(f"   - Cross-references: Consistent")
        print(f"   - Audio files: Present and valid")
        return 0
    else:
        print(f"\n⚠️  SOME ISSUES FOUND")
        print(f"   - {len(songs) - ready_count} songs have validation issues")
        print(f"   - Review individual song reports above")
        return 1

if __name__ == "__main__":
    exit(main())
