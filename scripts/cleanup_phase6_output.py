#!/usr/bin/env python3
"""
Clean up Phase 6 output directory by removing test files and organizing production data.

This script removes temporary files, old logs, and test data while preserving
the final optimized results for production use.

Author: TJAGen System Cleanup
Version: 1.0.0
"""

import os
import shutil
from pathlib import Path
import json
import time

def get_production_songs():
    """Get list of songs that should be kept for production."""
    # These are the songs from our final optimized test run
    production_songs = [
        "Ai Uta", "ABC", "1 2 3", "1 Dream", "ADAMAS", "ALIVE", 
        "Angel Dream", "Anpanman no March -New Audio-", "Ao no Sumika", 
        "Ashita no Hikari", "142 Toki no Watari-dori"
    ]
    return production_songs

def cleanup_candidate_audio(phase6_dir: Path, production_songs: list):
    """Clean up candidate audio files, keeping only production songs."""
    
    audio_dir = phase6_dir / "candidate_audio"
    if not audio_dir.exists():
        return
    
    print("🧹 Cleaning up candidate audio files...")
    
    # Get all audio files
    audio_files = list(audio_dir.glob("*.npy"))
    
    # Separate production files from test files
    production_files = []
    test_files = []
    
    for audio_file in audio_files:
        song_name = audio_file.stem.rsplit('_', 1)[0]  # Remove candidate ID
        if song_name in production_songs:
            production_files.append(audio_file)
        else:
            test_files.append(audio_file)
    
    print(f"  Found {len(production_files)} production audio files")
    print(f"  Found {len(test_files)} test audio files to remove")
    
    # Remove test files
    removed_count = 0
    for test_file in test_files:
        try:
            test_file.unlink()
            removed_count += 1
        except Exception as e:
            print(f"  ⚠️  Failed to remove {test_file}: {e}")
    
    print(f"  ✅ Removed {removed_count} test audio files")
    print(f"  ✅ Kept {len(production_files)} production audio files")

def cleanup_json_files(phase6_dir: Path, production_songs: list):
    """Clean up JSON files, keeping only production songs."""
    
    json_dirs = [
        "note_candidates",
        "candidate_features", 
        "detection_stats"
    ]
    
    for json_dir_name in json_dirs:
        json_dir = phase6_dir / json_dir_name
        if not json_dir.exists():
            continue
            
        print(f"🧹 Cleaning up {json_dir_name} files...")
        
        # Get all JSON files
        json_files = list(json_dir.glob("*.json"))
        
        # Separate production files from test files
        production_files = []
        test_files = []
        
        for json_file in json_files:
            # Extract song name from filename (remove suffix)
            song_name = json_file.stem
            for suffix in ["_candidates", "_features", "_stats"]:
                if song_name.endswith(suffix):
                    song_name = song_name[:-len(suffix)]
                    break
            
            if song_name in production_songs:
                production_files.append(json_file)
            else:
                test_files.append(json_file)
        
        print(f"  Found {len(production_files)} production {json_dir_name} files")
        print(f"  Found {len(test_files)} test {json_dir_name} files to remove")
        
        # Remove test files
        removed_count = 0
        for test_file in test_files:
            try:
                test_file.unlink()
                removed_count += 1
            except Exception as e:
                print(f"  ⚠️  Failed to remove {test_file}: {e}")
        
        print(f"  ✅ Removed {removed_count} test {json_dir_name} files")
        print(f"  ✅ Kept {len(production_files)} production {json_dir_name} files")

def cleanup_logs(phase6_dir: Path):
    """Clean up old log files, keeping only the most recent."""
    
    logs_dir = phase6_dir / "logs"
    if not logs_dir.exists():
        return
    
    print("🧹 Cleaning up log files...")
    
    # Get all log files
    log_files = list(logs_dir.glob("*.log"))
    
    if len(log_files) <= 1:
        print("  ✅ Only one log file found, keeping it")
        return
    
    # Sort by modification time, keep the most recent
    log_files.sort(key=lambda f: f.stat().st_mtime, reverse=True)
    
    # Keep the most recent log file
    keep_file = log_files[0]
    remove_files = log_files[1:]
    
    print(f"  Keeping most recent log: {keep_file.name}")
    print(f"  Removing {len(remove_files)} old log files")
    
    # Remove old log files
    removed_count = 0
    for log_file in remove_files:
        try:
            log_file.unlink()
            removed_count += 1
        except Exception as e:
            print(f"  ⚠️  Failed to remove {log_file}: {e}")
    
    print(f"  ✅ Removed {removed_count} old log files")

def update_processing_report(phase6_dir: Path, production_songs: list):
    """Update the processing report to reflect only production songs."""
    
    report_file = phase6_dir / "candidate_detection_report.json"
    if not report_file.exists():
        print("  ⚠️  No processing report found")
        return
    
    print("🧹 Updating processing report...")
    
    # Load current report
    with open(report_file, 'r') as f:
        report = json.load(f)
    
    # Filter results to keep only production songs
    original_results = report.get("results", [])
    production_results = [
        result for result in original_results 
        if result.get("song_name") in production_songs
    ]
    
    # Update report
    report["results"] = production_results
    report["cleanup_timestamp"] = time.strftime("%Y-%m-%d %H:%M:%S")
    report["cleanup_note"] = f"Cleaned up to keep only {len(production_songs)} production songs"
    
    # Recalculate summary statistics
    successful_results = [r for r in production_results if r.get("status") == "success"]
    
    if successful_results:
        total_candidates = sum(r.get("detection_stats", {}).get("total_candidates", 0) 
                             for r in successful_results)
        avg_candidates = total_candidates / len(successful_results)
        avg_processing_time = sum(r.get("processing_time", 0) for r in successful_results) / len(successful_results)
        
        report["summary"] = {
            "total_songs": len(production_results),
            "successful_songs": len(successful_results),
            "success_rate": len(successful_results) / len(production_results),
            "total_candidates": total_candidates,
            "avg_candidates_per_song": avg_candidates,
            "avg_processing_time": avg_processing_time
        }
    
    # Save updated report
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"  ✅ Updated processing report: {len(production_results)} songs kept")

def validate_cleanup(phase6_dir: Path, production_songs: list):
    """Validate that cleanup was successful."""
    
    print("🔍 Validating cleanup results...")
    
    # Check each directory
    dirs_to_check = [
        "note_candidates",
        "candidate_features", 
        "detection_stats",
        "candidate_audio"
    ]
    
    all_valid = True
    
    for dir_name in dirs_to_check:
        dir_path = phase6_dir / dir_name
        if not dir_path.exists():
            print(f"  ❌ Directory missing: {dir_name}")
            all_valid = False
            continue
        
        # Count files
        if dir_name == "candidate_audio":
            files = list(dir_path.glob("*.npy"))
        else:
            files = list(dir_path.glob("*.json"))
        
        # Check that we have files for each production song
        song_files = {}
        for file in files:
            if dir_name == "candidate_audio":
                song_name = file.stem.rsplit('_', 1)[0]
            else:
                song_name = file.stem
                for suffix in ["_candidates", "_features", "_stats"]:
                    if song_name.endswith(suffix):
                        song_name = song_name[:-len(suffix)]
                        break
            
            if song_name not in song_files:
                song_files[song_name] = 0
            song_files[song_name] += 1
        
        # Validate production songs are present
        missing_songs = set(production_songs) - set(song_files.keys())
        extra_songs = set(song_files.keys()) - set(production_songs)
        
        if missing_songs:
            print(f"  ❌ {dir_name}: Missing songs: {missing_songs}")
            all_valid = False
        
        if extra_songs:
            print(f"  ⚠️  {dir_name}: Extra songs found: {extra_songs}")
        
        if not missing_songs and not extra_songs:
            total_files = sum(song_files.values())
            print(f"  ✅ {dir_name}: {total_files} files for {len(song_files)} songs")
    
    return all_valid

def main():
    """Main cleanup function."""
    
    print("🚀 PHASE 6 OUTPUT CLEANUP")
    print("="*60)
    
    # Get paths
    phase6_dir = Path("data/processed/phase6")
    
    if not phase6_dir.exists():
        print("❌ Phase 6 output directory not found")
        return 1
    
    # Get production songs list
    production_songs = get_production_songs()
    print(f"📋 Production songs to keep: {len(production_songs)}")
    for song in production_songs:
        print(f"  - {song}")
    
    print(f"\n🧹 Starting cleanup process...")
    
    # Cleanup each component
    cleanup_candidate_audio(phase6_dir, production_songs)
    cleanup_json_files(phase6_dir, production_songs)
    cleanup_logs(phase6_dir)
    update_processing_report(phase6_dir, production_songs)
    
    # Validate cleanup
    print(f"\n🔍 Validating cleanup...")
    if validate_cleanup(phase6_dir, production_songs):
        print(f"\n✅ CLEANUP COMPLETED SUCCESSFULLY")
        print(f"   - Kept data for {len(production_songs)} production songs")
        print(f"   - Removed all test and temporary files")
        print(f"   - Updated processing report")
        print(f"   - System ready for production deployment")
    else:
        print(f"\n❌ CLEANUP VALIDATION FAILED")
        print(f"   - Some issues were detected")
        print(f"   - Please review the validation messages above")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
