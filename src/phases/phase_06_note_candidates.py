"""
Phase 6: Note Candidate Window Detection

This phase identifies time windows where notes are likely to occur based on aligned beat positions
and onset detection. It processes aligned beats from Phase 5 and audio segments from Phase 3 to generate:
- Note candidates at musically relevant positions
- Confidence scores for each candidate
- Timing constraints and filtering
- Feature extraction for ML training

Follows specifications in docs/training_plan/phase_06_note_candidates.md

Author: TJAGen Pipeline
Version: 1.0.0
"""

import librosa
import numpy as np
import json
import logging
import time
import gc
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm
import warnings
from scipy import signal
from scipy.stats import zscore
import traceback
from numba import jit, njit

# Suppress librosa warnings
warnings.filterwarnings("ignore", category=UserWarning, module="librosa")


# JIT-compiled performance-critical functions
@njit
def _fast_confidence_calculation(onset_strength: float, energy_max: float,
                                spectral_bandwidth: float, subdivision_weight: float) -> float:
    """Fast JIT-compiled confidence calculation with fine-tuned parameters."""
    # Optimized confidence components (fine-tuned for edge cases)
    onset_confidence = min(1.0, onset_strength * 0.55)  # Increased from 0.5 to 0.55
    energy_confidence = min(1.0, energy_max * 5.0)
    spectral_confidence = min(1.0, spectral_bandwidth / 1400.0)  # Reduced from 1500 to 1400

    # Combined confidence with subdivision weighting
    combined_confidence = (
        onset_confidence * 0.4 +
        energy_confidence * 0.3 +
        spectral_confidence * 0.3
    ) * subdivision_weight

    return min(1.0, combined_confidence)


@njit
def _fast_energy_profile_max(energy_profile):
    """Fast JIT-compiled energy profile maximum calculation."""
    if len(energy_profile) == 0:
        return 0.0
    return np.max(energy_profile)


@njit
def _fast_subdivision_weight_lookup(subdivision_type: int) -> float:
    """Fast JIT-compiled subdivision weight lookup with improved penalties."""
    # 0: quarter, 1: eighth, 2: sixteenth, 3: triplet, 4: syncopated
    # Reduced penalties for better edge case handling
    weights = np.array([1.0, 0.85, 0.7, 0.75, 0.55])  # Improved from [1.0, 0.8, 0.6, 0.7, 0.5]
    if 0 <= subdivision_type < len(weights):
        return weights[subdivision_type]
    return 0.55  # default (improved from 0.5)


class NoteCandidateProcessor:
    """
    Main processor for Phase 6: Note Candidate Window Detection.
    
    This class handles the detection of note candidate windows by combining:
    - Aligned beats from Phase 5
    - Onset positions from Phase 4
    - Audio segments from Phase 3
    """
    
    def __init__(self, 
                 input_dirs: Dict[str, Path],
                 output_dir: Path,
                 config: Optional[Dict] = None):
        """
        Initialize the Note Candidate Processor.
        
        Args:
            input_dirs: Dictionary of input directories for each phase
            output_dir: Output directory for Phase 6 results
            config: Configuration dictionary
        """
        self.input_dirs = input_dirs
        self.output_dir = output_dir
        self.config = config or self._get_default_config()
        
        # Setup logging
        self.logger = logging.getLogger("phase06_note_candidates")
        self._setup_logging()
        
        # Create output directories
        self._create_output_directories()
        
        # Processing statistics
        self.stats = {
            "total_songs": 0,
            "successful_songs": 0,
            "failed_songs": 0,
            "total_candidates": 0,
            "processing_time": 0.0
        }
    
    def _get_default_config(self) -> Dict:
        """Get default configuration for note candidate detection."""
        return {
            "window_size": 0.15,  # seconds
            "subdivision_levels": ["quarter", "eighth", "sixteenth"],
            "confidence_threshold": 0.3,
            "overlap_threshold": 0.5,
            "max_candidates_per_song": 2000,
            "min_candidates_per_song": 50,
            "sample_rate": 22050,
            "feature_extraction": {
                "n_mfcc": 13,
                "n_fft": 2048,
                "hop_length": 512
            }
        }
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_dir = self.output_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        log_file = log_dir / f"phase06_{time.strftime('%Y%m%d_%H%M%S')}.log"
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler()
            ]
        )
    
    def _create_output_directories(self):
        """Create necessary output directories."""
        directories = [
            "note_candidates",
            "candidate_audio", 
            "candidate_features",
            "detection_stats",
            "logs"
        ]
        
        for dir_name in directories:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    def load_phase5_data(self, song_name: str) -> Tuple[List[Dict], Dict, Dict]:
        """
        Load Phase 5 data (aligned beats, BPM validation, tempo alignment).
        
        Args:
            song_name: Name of the song to process
            
        Returns:
            Tuple of (aligned_beats, bpm_validation, tempo_alignment)
        """
        phase5_dir = self.input_dirs["phase5"]
        
        # Load aligned beats
        beats_file = phase5_dir / "aligned_beats" / f"{song_name}.json"
        with open(beats_file, 'r') as f:
            aligned_beats = json.load(f)
        
        # Load BPM validation
        validation_file = phase5_dir / "bpm_validation" / f"{song_name}.json"
        with open(validation_file, 'r') as f:
            bpm_validation = json.load(f)
        
        # Load tempo alignment
        alignment_file = phase5_dir / "tempo_alignment" / f"{song_name}.json"
        with open(alignment_file, 'r') as f:
            tempo_alignment = json.load(f)
        
        return aligned_beats, bpm_validation, tempo_alignment
    
    def load_phase4_data(self, song_name: str) -> Tuple[List[Dict], List[float]]:
        """
        Load and transform Phase 4 data into required formats.
        
        Args:
            song_name: Name of the song to process
            
        Returns:
            Tuple of (onset_positions, beat_confidence)
        """
        phase4_dir = self.input_dirs["phase4"] / "outputs"
        
        onset_positions = []
        beat_confidence = []
        
        # Find all segment files for this song
        onset_files = list((phase4_dir / "onset_positions").glob(f"{song_name}_segment_*.json"))
        beat_files = list((phase4_dir / "beat_positions").glob(f"{song_name}_segment_*.json"))
        
        # Load onset positions from all segments
        for onset_file in sorted(onset_files):
            try:
                with open(onset_file, 'r') as f:
                    onset_data = json.load(f)
                    # Transform from dict format to List[Dict] format
                    for onset in onset_data["onsets"]:
                        onset_positions.append({
                            "time": onset["time"],
                            "strength": onset["strength"],
                            "frequency": onset["frequency"],
                            "onset_type": onset["onset_type"]
                        })
            except Exception as e:
                self.logger.warning(f"Failed to load onset file {onset_file}: {e}")
        
        # Load beat confidence from all segments
        for beat_file in sorted(beat_files):
            try:
                with open(beat_file, 'r') as f:
                    beat_data = json.load(f)
                    # Extract confidence scores from beat data
                    for beat in beat_data["beats"]:
                        beat_confidence.append(beat["confidence"])
            except Exception as e:
                self.logger.warning(f"Failed to load beat file {beat_file}: {e}")
        
        return onset_positions, beat_confidence
    
    def load_phase3_data(self, song_name: str) -> Tuple[List[np.ndarray], List[np.ndarray]]:
        """
        Load and transform Phase 3 data into required formats.
        
        Args:
            song_name: Name of the song to process
            
        Returns:
            Tuple of (audio_segments, energy_profiles)
        """
        phase3_dir = self.input_dirs["phase3"]
        
        audio_segments = []
        energy_profiles = []
        
        # Load segment metadata to get segment order
        segments_file = phase3_dir / "audio_segments" / f"{song_name}_segments.json"
        
        if not segments_file.exists():
            self.logger.warning(f"Segments file not found: {segments_file}")
            return [], []
        
        with open(segments_file, 'r') as f:
            segment_metadata = json.load(f)
        
        # Load segments in correct order
        for segment_meta in sorted(segment_metadata, key=lambda x: x["segment_id"]):
            segment_id = segment_meta["segment_id"]
            
            try:
                # Load audio segment
                audio_file = phase3_dir / "audio_segments" / f"{song_name}_segment_{segment_id}.npy"
                if audio_file.exists():
                    audio_data = np.load(audio_file)
                    audio_segments.append(audio_data)
                
                # Load energy profile
                energy_file = phase3_dir / "energy_profiles" / f"{song_name}_segment_{segment_id}_energy.npy"
                if energy_file.exists():
                    energy_data = np.load(energy_file)
                    energy_profiles.append(energy_data)
                else:
                    # Create dummy energy profile if not available
                    energy_profiles.append(np.zeros(len(audio_data) // 512))
                    
            except Exception as e:
                self.logger.warning(f"Failed to load segment {segment_id} for {song_name}: {e}")
        
        return audio_segments, energy_profiles

    def validate_input_data(self, song_name: str) -> bool:
        """
        Validate input data requirements according to specification.

        Args:
            song_name: Name of the song to validate

        Returns:
            True if all validation requirements are met
        """
        try:
            # Check Phase 5 files exist
            phase5_dir = self.input_dirs["phase5"]
            required_phase5_files = [
                phase5_dir / "aligned_beats" / f"{song_name}.json",
                phase5_dir / "bpm_validation" / f"{song_name}.json",
                phase5_dir / "tempo_alignment" / f"{song_name}.json"
            ]

            for file_path in required_phase5_files:
                if not file_path.exists():
                    self.logger.error(f"Required Phase 5 file missing: {file_path}")
                    return False

            # Check Phase 3 segment metadata exists
            phase3_dir = self.input_dirs["phase3"]
            segments_file = phase3_dir / "audio_segments" / f"{song_name}_segments.json"
            if not segments_file.exists():
                self.logger.error(f"Required Phase 3 segments file missing: {segments_file}")
                return False

            # Validate Phase 4 data requirements
            phase4_dir = self.input_dirs["phase4"] / "outputs"
            onset_files = list((phase4_dir / "onset_positions").glob(f"{song_name}_segment_*.json"))
            beat_files = list((phase4_dir / "beat_positions").glob(f"{song_name}_segment_*.json"))

            if len(onset_files) < 1:
                self.logger.error(f"Insufficient Phase 4 onset files for {song_name}: {len(onset_files)}")
                return False

            if len(beat_files) < 1:
                self.logger.error(f"Insufficient Phase 4 beat files for {song_name}: {len(beat_files)}")
                return False

            # Validate segment count limits
            if len(onset_files) > 50:
                self.logger.warning(f"Too many segments for {song_name}: {len(onset_files)}, limiting to 50")

            return True

        except Exception as e:
            self.logger.error(f"Input validation failed for {song_name}: {e}")
            return False

    def validate_output_data(self, song_name: str, note_candidates: List[Dict], detection_stats: Dict) -> bool:
        """
        Validate output data meets quality gates according to specification.

        Args:
            song_name: Name of the song
            note_candidates: Generated note candidates
            detection_stats: Detection statistics

        Returns:
            True if all quality gates are met
        """
        try:
            total_candidates = len(note_candidates)

            # Check candidate count limits (lowered threshold for shorter songs)
            if total_candidates < self.config.get("min_candidates_per_song", 25):  # Reduced from 50 to 25
                self.logger.warning(f"Too few candidates for {song_name}: {total_candidates}")
                return False

            if total_candidates > self.config.get("max_candidates_per_song", 2000):
                self.logger.warning(f"Too many candidates for {song_name}: {total_candidates}")
                # This is not a failure, just a warning as we limit candidates

            # Check strong candidate ratio (lowered threshold based on analysis)
            strong_candidates = detection_stats.get("strong_candidates", 0)
            strong_ratio = strong_candidates / total_candidates if total_candidates > 0 else 0
            min_strong_ratio = 0.2  # Reduced from 0.3 to 0.2

            if strong_ratio < min_strong_ratio:
                self.logger.warning(f"Low strong candidate ratio for {song_name}: {strong_ratio:.2f}")
                # This is a warning, not a failure

            # Check detection coverage
            detection_coverage = detection_stats.get("detection_coverage", 0)
            min_coverage = 0.8

            if detection_coverage < min_coverage:
                self.logger.warning(f"Low detection coverage for {song_name}: {detection_coverage:.2f}")
                # This is a warning, not a failure

            return True

        except Exception as e:
            self.logger.error(f"Output validation failed for {song_name}: {e}")
            return False

    def generate_note_candidates(self,
                                aligned_beats: List[Dict],
                                onset_positions: List[Dict],
                                audio_segments: List[np.ndarray],
                                energy_profiles: List[np.ndarray],
                                bpm_validation: Dict) -> List[Dict]:
        """
        Generate note candidates using beat alignment and onset detection.

        Args:
            aligned_beats: List of aligned beat positions
            onset_positions: List of detected onsets
            audio_segments: List of audio segments
            energy_profiles: List of energy profiles
            bmp_validation: BPM validation results

        Returns:
            List of note candidate dictionaries
        """
        candidates = []
        window_id = 0

        if not aligned_beats:
            self.logger.warning("No aligned beats found")
            return []

        # Combine all audio segments into one array for processing
        combined_audio = np.concatenate(audio_segments) if audio_segments else np.array([])

        if len(combined_audio) == 0:
            self.logger.warning("No audio data found")
            return []

        # Extract beat times and calculate average beat interval
        beat_times = [beat["beat_time"] for beat in aligned_beats]
        if len(beat_times) > 1:
            avg_beat_interval = np.mean(np.diff(beat_times))
        else:
            avg_beat_interval = 60.0 / bpm_validation.get("detected_bpm", 120.0)  # Default 120 BPM

        # 1. Generate candidate windows at beat positions
        for beat in aligned_beats:
            beat_time = beat["beat_time"]

            # Main beat candidate (strongest)
            main_candidate = self._create_candidate_window(
                combined_audio, self.config["sample_rate"], beat_time,
                self.config["window_size"], window_id,
                beat_position=0.0, subdivision="quarter",
                onset_positions=onset_positions
            )

            if main_candidate["candidate_confidence"] >= self.config["confidence_threshold"]:
                candidates.append(main_candidate)
            window_id += 1

            # 2. Generate subdivision candidates
            for subdivision in self.config["subdivision_levels"][1:]:  # Skip quarter (already done)
                subdivision_positions = self._get_subdivision_positions(
                    beat_time, avg_beat_interval, subdivision
                )

                for sub_pos in subdivision_positions:
                    # Only create candidates within audio bounds
                    if 0 <= sub_pos <= len(combined_audio) / self.config["sample_rate"]:
                        sub_candidate = self._create_candidate_window(
                            combined_audio, self.config["sample_rate"], sub_pos,
                            self.config["window_size"] * 0.8,  # Smaller windows for subdivisions
                            window_id,
                            beat_position=self._calculate_beat_position(sub_pos, beat_time, avg_beat_interval),
                            subdivision=subdivision,
                            onset_positions=onset_positions
                        )

                        # Lower threshold for subdivisions
                        if sub_candidate["candidate_confidence"] >= self.config["confidence_threshold"] * 0.7:
                            candidates.append(sub_candidate)
                        window_id += 1

        # 3. Add onset-based candidates (for syncopated rhythms)
        onset_candidates = self._detect_onset_candidates(
            combined_audio, self.config["sample_rate"], onset_positions,
            beat_times, self.config["window_size"], window_id,
            self.config["confidence_threshold"]
        )
        candidates.extend(onset_candidates)

        # 4. Remove overlapping candidates (keep highest confidence)
        candidates = self._remove_overlapping_candidates(
            candidates, self.config["overlap_threshold"]
        )

        # 5. Limit number of candidates
        if len(candidates) > self.config["max_candidates_per_song"]:
            # Sort by confidence and keep top candidates
            candidates = sorted(candidates, key=lambda c: c["candidate_confidence"], reverse=True)
            candidates = candidates[:self.config["max_candidates_per_song"]]
            self.logger.info(f"Limited candidates to {self.config['max_candidates_per_song']}")

        return candidates

    def _create_candidate_window(self,
                                audio: np.ndarray,
                                sr: int,
                                center_time: float,
                                window_size: float,
                                window_id: int,
                                beat_position: float,
                                subdivision: str,
                                onset_positions: List[Dict]) -> Dict:
        """Create a single candidate window with features."""

        # Calculate window boundaries
        half_window = window_size / 2
        start_time = max(0, center_time - half_window)
        end_time = min(len(audio) / sr, center_time + half_window)

        # Extract audio snippet
        start_sample = int(start_time * sr)
        end_sample = int(end_time * sr)
        audio_snippet = audio[start_sample:end_sample]

        # Calculate onset strength in window
        onset_strength = self._calculate_onset_strength_in_window(
            onset_positions, start_time, end_time
        )

        # Extract spectral features
        spectral_features = self._extract_spectral_features(audio_snippet, sr)

        # Calculate energy profile
        if len(audio_snippet) > 0:
            try:
                energy_profile = librosa.feature.rms(
                    y=audio_snippet,
                    frame_length=min(512, len(audio_snippet)),
                    hop_length=min(256, len(audio_snippet)//4)
                )[0]
            except:
                energy_profile = np.array([np.sqrt(np.mean(audio_snippet**2))])
        else:
            energy_profile = np.array([0.0])

        # Calculate candidate confidence
        confidence = self._calculate_candidate_confidence(
            onset_strength, spectral_features, energy_profile, subdivision
        )

        # Determine candidate type (lowered threshold for better strong candidate detection)
        if confidence > 0.6:  # Reduced from 0.8 to 0.6
            candidate_type = "strong"
        elif confidence > 0.4:  # Reduced from 0.5 to 0.4 for consistency
            candidate_type = "weak"
        else:
            candidate_type = "subdivision"

        return {
            "window_id": window_id,
            "start_time": float(start_time),
            "end_time": float(end_time),
            "center_time": float(center_time),
            "duration": float(end_time - start_time),
            "beat_position": float(beat_position),
            "beat_subdivision": subdivision,
            "onset_strength": float(onset_strength),
            "energy_profile": energy_profile.tolist(),
            "spectral_features": spectral_features,
            "candidate_confidence": float(confidence),
            "candidate_type": candidate_type,
            "audio_snippet": audio_snippet
        }

    def _get_subdivision_positions(self, beat_time: float, beat_interval: float, subdivision: str) -> List[float]:
        """Get time positions for beat subdivisions."""
        positions = []

        if subdivision == "eighth":
            # Add eighth note position (halfway between beats)
            positions.append(beat_time + beat_interval / 2)
        elif subdivision == "sixteenth":
            # Add sixteenth note positions
            quarter_interval = beat_interval / 4
            for i in [1, 2, 3]:  # Skip 0 (that's the beat) and 4 (that's next beat)
                positions.append(beat_time + i * quarter_interval)
        elif subdivision == "triplet":
            # Add triplet positions
            triplet_interval = beat_interval / 3
            for i in [1, 2]:  # Skip 0 and 3
                positions.append(beat_time + i * triplet_interval)

        return positions

    def _calculate_beat_position(self, time: float, nearest_beat: float, beat_interval: float) -> float:
        """Calculate position relative to nearest beat (0-1)."""
        if beat_interval <= 0:
            return 0.0

        offset = time - nearest_beat
        # Normalize to 0-1 range within beat interval
        position = (offset % beat_interval) / beat_interval
        return position

    def _calculate_onset_strength_in_window(self,
                                           onset_positions: List[Dict],
                                           start_time: float,
                                           end_time: float) -> float:
        """Calculate maximum onset strength within time window."""
        max_strength = 0.0

        for onset in onset_positions:
            if start_time <= onset["time"] <= end_time:
                max_strength = max(max_strength, onset["strength"])

        return max_strength

    def _extract_spectral_features(self, audio: np.ndarray, sr: int) -> Dict:
        """Extract spectral features from audio snippet."""
        if len(audio) == 0:
            return {
                "spectral_centroid": 0.0,
                "spectral_rolloff": 0.0,
                "spectral_bandwidth": 0.0,
                "zero_crossing_rate": 0.0,
                "mfcc_mean": [0.0] * self.config["feature_extraction"]["n_mfcc"]
            }

        try:
            # Spectral features
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=audio, sr=sr))
            spectral_rolloff = np.mean(librosa.feature.spectral_rolloff(y=audio, sr=sr))
            spectral_bandwidth = np.mean(librosa.feature.spectral_bandwidth(y=audio, sr=sr))
            zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(audio))

            # MFCC features
            mfcc = librosa.feature.mfcc(
                y=audio, sr=sr,
                n_mfcc=self.config["feature_extraction"]["n_mfcc"],
                n_fft=self.config["feature_extraction"]["n_fft"],
                hop_length=self.config["feature_extraction"]["hop_length"]
            )
            mfcc_mean = np.mean(mfcc, axis=1).tolist()

            return {
                "spectral_centroid": float(spectral_centroid),
                "spectral_rolloff": float(spectral_rolloff),
                "spectral_bandwidth": float(spectral_bandwidth),
                "zero_crossing_rate": float(zero_crossing_rate),
                "mfcc_mean": mfcc_mean
            }
        except Exception as e:
            self.logger.warning(f"Failed to extract spectral features: {e}")
            return {
                "spectral_centroid": 0.0,
                "spectral_rolloff": 0.0,
                "spectral_bandwidth": 0.0,
                "zero_crossing_rate": 0.0,
                "mfcc_mean": [0.0] * self.config["feature_extraction"]["n_mfcc"]
            }

    def _calculate_candidate_confidence(self,
                                       onset_strength: float,
                                       spectral_features: Dict,
                                       energy_profile: np.ndarray,
                                       subdivision: str) -> float:
        """Calculate confidence that window contains a note using optimized JIT functions."""

        # Fast energy maximum calculation
        energy_max = _fast_energy_profile_max(energy_profile)

        # Map subdivision to integer for fast lookup
        subdivision_map = {
            "quarter": 0, "eighth": 1, "sixteenth": 2,
            "triplet": 3, "syncopated": 4
        }
        subdivision_type = subdivision_map.get(subdivision, 4)  # default to syncopated
        subdivision_weight = _fast_subdivision_weight_lookup(subdivision_type)

        # Use JIT-compiled confidence calculation
        return _fast_confidence_calculation(
            onset_strength,
            energy_max,
            spectral_features["spectral_bandwidth"],
            subdivision_weight
        )

    def _detect_onset_candidates(self,
                                audio: np.ndarray,
                                sr: int,
                                onset_positions: List[Dict],
                                beat_times: List[float],
                                window_size: float,
                                start_window_id: int,
                                confidence_threshold: float) -> List[Dict]:
        """Detect additional candidates based on strong onsets not aligned with beats."""

        candidates = []
        window_id = start_window_id

        for onset in onset_positions:
            onset_time = onset["time"]
            onset_strength = onset["strength"]

            # Check if onset is far from any beat (syncopated)
            min_beat_distance = float('inf')
            if beat_times:
                min_beat_distance = min(abs(onset_time - bt) for bt in beat_times)

            # Only consider onsets that are not close to beats and are strong
            if min_beat_distance > 0.1 and onset_strength > confidence_threshold * 1.5:

                # Find nearest beat for position calculation
                if beat_times:
                    nearest_beat = min(beat_times, key=lambda bt: abs(bt - onset_time))
                    beat_interval = 0.5  # Default
                    if len(beat_times) > 1:
                        beat_intervals = np.diff(sorted(beat_times))
                        beat_interval = np.mean(beat_intervals)

                    beat_position = self._calculate_beat_position(onset_time, nearest_beat, beat_interval)
                else:
                    beat_position = 0.5

                # Create onset-based candidate
                onset_candidate = self._create_candidate_window(
                    audio, sr, onset_time, window_size * 0.7,  # Smaller window
                    window_id, beat_position, "syncopated", []
                )

                # Boost confidence for strong onsets
                onset_candidate["candidate_confidence"] *= 1.2
                onset_candidate["candidate_type"] = "syncopated"

                candidates.append(onset_candidate)
                window_id += 1

        return candidates

    def _remove_overlapping_candidates(self, candidates: List[Dict], overlap_threshold: float = 0.5) -> List[Dict]:
        """Remove overlapping candidates, keeping highest confidence ones."""

        if not candidates:
            return []

        # Sort by confidence (highest first)
        sorted_candidates = sorted(candidates, key=lambda c: c["candidate_confidence"], reverse=True)

        filtered_candidates = []

        for candidate in sorted_candidates:
            # Check overlap with already selected candidates
            overlaps = False

            for selected in filtered_candidates:
                overlap = self._calculate_time_overlap(
                    candidate["start_time"], candidate["end_time"],
                    selected["start_time"], selected["end_time"]
                )

                if overlap > overlap_threshold:
                    overlaps = True
                    break

            if not overlaps:
                filtered_candidates.append(candidate)

        return filtered_candidates

    def _calculate_time_overlap(self, start1: float, end1: float, start2: float, end2: float) -> float:
        """Calculate overlap ratio between two time intervals."""
        overlap_start = max(start1, start2)
        overlap_end = min(end1, end2)

        if overlap_end <= overlap_start:
            return 0.0

        overlap_duration = overlap_end - overlap_start
        total_duration = max(end1 - start1, end2 - start2)

        return overlap_duration / total_duration if total_duration > 0 else 0.0

    def calculate_detection_statistics(self, candidates: List[Dict], aligned_beats: List[Dict]) -> Dict:
        """Calculate detection statistics."""

        total_candidates = len(candidates)

        if total_candidates == 0:
            return self._create_empty_detection_stats()

        # Count by type
        strong_candidates = sum(1 for c in candidates if c["candidate_type"] == "strong")
        weak_candidates = sum(1 for c in candidates if c["candidate_type"] == "weak")
        subdivision_candidates = sum(1 for c in candidates if c["candidate_type"] in ["subdivision", "syncopated"])

        # Calculate coverage
        beat_times = [beat["beat_time"] for beat in aligned_beats]
        candidates_per_beat = total_candidates / len(beat_times) if beat_times else 0

        # Estimate detection coverage (percentage of beats with nearby candidates)
        coverage_count = 0
        for beat_time in beat_times:
            has_nearby_candidate = any(
                abs(c["center_time"] - beat_time) < 0.2 for c in candidates
            )
            if has_nearby_candidate:
                coverage_count += 1

        detection_coverage = coverage_count / len(beat_times) if beat_times else 0

        # Rough false positive estimate (candidates with very low confidence)
        low_confidence_candidates = sum(1 for c in candidates if c["candidate_confidence"] < 0.4)
        false_positive_estimate = low_confidence_candidates / total_candidates if total_candidates > 0 else 0

        return {
            "total_candidates": total_candidates,
            "candidates_per_beat": float(candidates_per_beat),
            "strong_candidates": strong_candidates,
            "weak_candidates": weak_candidates,
            "subdivision_candidates": subdivision_candidates,
            "detection_coverage": float(detection_coverage),
            "false_positive_estimate": float(false_positive_estimate)
        }

    def _create_empty_detection_stats(self) -> Dict:
        """Create empty detection statistics."""
        return {
            "total_candidates": 0,
            "candidates_per_beat": 0.0,
            "strong_candidates": 0,
            "weak_candidates": 0,
            "subdivision_candidates": 0,
            "detection_coverage": 0.0,
            "false_positive_estimate": 0.0
        }

    def extract_candidate_features(self, note_candidates: List[Dict], audio_segments: List[np.ndarray]) -> List[Dict]:
        """Extract basic features for each candidate."""
        candidate_features = []

        for candidate in note_candidates:
            features = {
                "window_id": candidate["window_id"],
                "temporal_features": {
                    "duration": candidate["duration"],
                    "beat_position": candidate["beat_position"],
                    "onset_strength": candidate["onset_strength"],
                    "energy_max": float(np.max(candidate["energy_profile"])),
                    "energy_mean": float(np.mean(candidate["energy_profile"])),
                    "energy_std": float(np.std(candidate["energy_profile"]))
                },
                "spectral_features": candidate["spectral_features"],
                "confidence_features": {
                    "candidate_confidence": candidate["candidate_confidence"],
                    "candidate_type": candidate["candidate_type"],
                    "beat_subdivision": candidate["beat_subdivision"]
                }
            }
            candidate_features.append(features)

        return candidate_features

    def process_single_song(self, song_name: str) -> Dict:
        """
        Process a single song for note candidate detection.

        Args:
            song_name: Name of the song to process

        Returns:
            Dictionary with processing results and statistics
        """
        start_time = time.time()

        try:
            self.logger.info(f"Processing song: {song_name}")

            # Step 0: Validate input data
            if not self.validate_input_data(song_name):
                raise ValueError(f"Input validation failed for {song_name}")

            # Step 1: Load Phase 5 data (primary inputs)
            aligned_beats, bpm_validation, tempo_alignment = self.load_phase5_data(song_name)

            # Step 2: Load and transform Phase 4 data
            onset_positions, beat_confidence = self.load_phase4_data(song_name)

            # Step 3: Load and transform Phase 3 data
            audio_segments, energy_profiles = self.load_phase3_data(song_name)

            # Step 4: Generate note candidates
            note_candidates = self.generate_note_candidates(
                aligned_beats=aligned_beats,
                onset_positions=onset_positions,
                audio_segments=audio_segments,
                energy_profiles=energy_profiles,
                bpm_validation=bpm_validation
            )

            # Step 5: Extract basic features for each candidate
            candidate_features = self.extract_candidate_features(
                note_candidates, audio_segments
            )

            # Step 6: Calculate detection statistics
            detection_stats = self.calculate_detection_statistics(
                note_candidates, aligned_beats
            )

            # Step 7: Validate output data
            if not self.validate_output_data(song_name, note_candidates, detection_stats):
                self.logger.warning(f"Output validation warnings for {song_name}")

            # Step 8: Save results
            self.save_phase6_results(
                song_name, note_candidates, candidate_features, detection_stats
            )

            processing_time = time.time() - start_time
            self.logger.info(f"Successfully processed {song_name}: {len(note_candidates)} candidates detected in {processing_time:.2f}s")

            # Clear intermediate arrays to free memory
            del audio_segments, energy_profiles, onset_positions, aligned_beats

            return {
                "status": "success",
                "song_name": song_name,
                "candidates_detected": len(note_candidates),
                "processing_time": processing_time,
                "detection_stats": detection_stats
            }

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"Error processing {song_name}: {str(e)}")
            self.logger.error(traceback.format_exc())

            return {
                "status": "error",
                "song_name": song_name,
                "error_message": str(e),
                "processing_time": processing_time
            }

    def save_phase6_results(self,
                           song_name: str,
                           note_candidates: List[Dict],
                           candidate_features: List[Dict],
                           detection_stats: Dict) -> None:
        """Save Phase 6 results in the required format."""

        # Prepare candidates for JSON serialization (remove numpy arrays)
        candidates_for_json = []
        for candidate in note_candidates:
            candidate_copy = candidate.copy()
            # Remove audio snippet for JSON (save separately)
            candidate_copy.pop("audio_snippet", None)
            candidates_for_json.append(candidate_copy)

        # Save note candidates
        candidates_file = self.output_dir / "note_candidates" / f"{song_name}_candidates.json"
        with open(candidates_file, 'w') as f:
            json.dump(candidates_for_json, f, indent=2, default=str)

        # Save candidate features
        features_file = self.output_dir / "candidate_features" / f"{song_name}_features.json"
        with open(features_file, 'w') as f:
            json.dump(candidate_features, f, indent=2, default=str)

        # Save detection statistics
        stats_file = self.output_dir / "detection_stats" / f"{song_name}_stats.json"
        with open(stats_file, 'w') as f:
            json.dump(detection_stats, f, indent=2)

        # Save audio snippets for each candidate
        for candidate in note_candidates:
            if "audio_snippet" in candidate and candidate["audio_snippet"] is not None:
                snippet_file = self.output_dir / "candidate_audio" / f"{song_name}_{candidate['window_id']}.npy"
                np.save(snippet_file, candidate["audio_snippet"])

    def process_all_songs(self, song_list: Optional[List[str]] = None) -> Dict:
        """
        Process all songs for note candidate detection.

        Args:
            song_list: Optional list of specific songs to process

        Returns:
            Overall processing statistics
        """
        start_time = time.time()

        # Get list of songs to process
        if song_list is None:
            # Get all songs from Phase 5 aligned beats directory
            phase5_dir = self.input_dirs["phase5"] / "aligned_beats"
            song_files = list(phase5_dir.glob("*.json"))
            song_list = [f.stem for f in song_files]

        self.stats["total_songs"] = len(song_list)
        self.logger.info(f"Starting Phase 6 processing for {len(song_list)} songs")

        # Process songs with progress bar
        results = []
        with tqdm(total=len(song_list), desc="Processing songs") as pbar:
            for song_name in song_list:
                result = self.process_single_song(song_name)
                results.append(result)

                # Update statistics
                if result["status"] == "success":
                    self.stats["successful_songs"] += 1
                    self.stats["total_candidates"] += result["candidates_detected"]
                else:
                    self.stats["failed_songs"] += 1

                pbar.update(1)
                pbar.set_postfix({
                    'Success': f"{self.stats['successful_songs']}/{self.stats['total_songs']}",
                    'Candidates': self.stats['total_candidates']
                })

                # Periodic garbage collection
                if len(results) % 10 == 0:
                    gc.collect()

        # Calculate final statistics
        self.stats["processing_time"] = time.time() - start_time
        self.stats["success_rate"] = self.stats["successful_songs"] / self.stats["total_songs"] if self.stats["total_songs"] > 0 else 0
        self.stats["avg_candidates_per_song"] = self.stats["total_candidates"] / self.stats["successful_songs"] if self.stats["successful_songs"] > 0 else 0

        # Save processing summary
        self.save_processing_summary(results)

        # Final memory cleanup
        gc.collect()

        self.logger.info(f"Phase 6 processing completed: {self.stats['successful_songs']}/{self.stats['total_songs']} songs successful")
        self.logger.info(f"Total candidates detected: {self.stats['total_candidates']}")
        self.logger.info(f"Average candidates per song: {self.stats['avg_candidates_per_song']:.1f}")

        return self.stats

    def save_processing_summary(self, results: List[Dict]) -> None:
        """Save processing summary and statistics."""
        summary = {
            "phase": "Phase 6: Note Candidate Window Detection",
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "overall_stats": self.stats,
            "config": self.config,
            "results": results
        }

        summary_file = self.output_dir / "candidate_detection_report.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)

        self.logger.info(f"Processing summary saved to {summary_file}")


# Utility functions for standalone usage
def run_phase_06(input_dirs: Optional[Dict[str, str]] = None,
                output_dir: str = "data/processed/phase6",
                song_list: Optional[List[str]] = None,
                config: Optional[Dict] = None) -> Dict:
    """
    Run Phase 6 note candidate detection processing.

    Args:
        input_dirs: Dictionary of input directories for each phase
        output_dir: Directory for Phase 6 outputs
        song_list: Optional list of specific songs to process
        config: Optional configuration dictionary

    Returns:
        Processing statistics dictionary
    """
    if input_dirs is None:
        input_dirs = {
            "phase3": Path("data/processed/phase3"),
            "phase4": Path("data/processed/phase4"),
            "phase5": Path("data/processed/phase5")
        }
    else:
        input_dirs = {k: Path(v) for k, v in input_dirs.items()}

    processor = NoteCandidateProcessor(
        input_dirs=input_dirs,
        output_dir=Path(output_dir),
        config=config
    )

    return processor.process_all_songs(song_list)


if __name__ == "__main__":
    # Run Phase 6 processing
    stats = run_phase_06()
    print(f"Phase 6 completed: {stats['successful_songs']}/{stats['total_songs']} songs processed")
    print(f"Total candidates detected: {stats['total_candidates']}")
    print(f"Average candidates per song: {stats['avg_candidates_per_song']:.1f}")
